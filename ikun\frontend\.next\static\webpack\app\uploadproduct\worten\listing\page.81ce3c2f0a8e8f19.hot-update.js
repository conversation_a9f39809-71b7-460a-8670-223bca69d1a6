"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态 - 产品状态改为单选\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 单选状态\n    ;\n    const [selectedTranslationStatus, setSelectedTranslationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 翻译状态也改为单选\n    ;\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数 - 改为单选模式\n    const handleStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleTranslationStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedTranslationStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleReset = ()=>{\n        setSelectedStatus(\"\");\n        setSelectedTranslationStatus(\"\");\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatus) {\n            params.status = selectedStatus;\n        }\n        if (selectedTranslationStatus) {\n            params.translation_status = selectedTranslationStatus;\n        }\n        if (dateRange.start) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end) {\n            params.end_date = dateRange.end;\n        }\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatus,\n        selectedTranslationStatus,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 474,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 487,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 594,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 646,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 645,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 658,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 657,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: selectedStatus === \"\" ? \"default\" : \"outline\",\n                                                                onClick: ()=>setSelectedStatus(\"\"),\n                                                                className: \"h-8\",\n                                                                children: \"全部\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            statusOptions.map((option)=>{\n                                                                const isSelected = selectedStatus === option.value;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: isSelected ? \"default\" : \"outline\",\n                                                                    onClick: ()=>handleStatusSelect(option.value),\n                                                                    className: \"h-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        option.label\n                                                                    ]\n                                                                }, option.value, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: translationStatusOptions.map((option)=>{\n                                                            const isSelected = selectedTranslationStatus === option.value;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: isSelected ? \"default\" : \"outline\",\n                                                                onClick: ()=>handleTranslationStatusSelect(option.value),\n                                                                className: \"h-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"翻译-\",\n                                                                    option.label\n                                                                ]\n                                                            }, option.value, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-80 p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium mb-2 block\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                            placeholder: \"年\",\n                                                                                            maxLength: 4,\n                                                                                            className: \"w-16 text-center\",\n                                                                                            value: dateRange.start ? dateRange.start.split(\"-\")[0] : \"\",\n                                                                                            onChange: (e)=>{\n                                                                                                const year = e.target.value.replace(/\\D/g, \"\").slice(0, 4);\n                                                                                                const currentStart = dateRange.start || \"----\";\n                                                                                                const parts = currentStart.split(\"-\");\n                                                                                                const newDate = \"\".concat(year.padEnd(4, \"-\"), \"-\").concat(parts[1] || \"--\", \"-\").concat(parts[2] || \"--\");\n                                                                                                if (year.length === 4 || year === \"\") {\n                                                                                                    setDateRange((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            start: year === \"\" ? \"\" : newDate.replace(/-+$/, \"\")\n                                                                                                        }));\n                                                                                                }\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 821,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"/\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 839,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                            placeholder: \"月\",\n                                                                                            maxLength: 2,\n                                                                                            className: \"w-12 text-center\",\n                                                                                            value: dateRange.start ? dateRange.start.split(\"-\")[1] || \"\" : \"\",\n                                                                                            onChange: (e)=>{\n                                                                                                const month = e.target.value.replace(/\\D/g, \"\").slice(0, 2);\n                                                                                                const currentStart = dateRange.start || \"----\";\n                                                                                                const parts = currentStart.split(\"-\");\n                                                                                                if (month === \"\" || parseInt(month) >= 1 && parseInt(month) <= 12) {\n                                                                                                    const newDate = \"\".concat(parts[0] || \"----\", \"-\").concat(month.padStart(2, \"0\"), \"-\").concat(parts[2] || \"--\");\n                                                                                                    setDateRange((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            start: month === \"\" ? parts[0] || \"\" : newDate.replace(/-+$/, \"\")\n                                                                                                        }));\n                                                                                                }\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 840,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"/\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 858,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                            placeholder: \"日\",\n                                                                                            maxLength: 2,\n                                                                                            className: \"w-12 text-center\",\n                                                                                            value: dateRange.start ? dateRange.start.split(\"-\")[2] || \"\" : \"\",\n                                                                                            onChange: (e)=>{\n                                                                                                const day = e.target.value.replace(/\\D/g, \"\").slice(0, 2);\n                                                                                                const currentStart = dateRange.start || \"----\";\n                                                                                                const parts = currentStart.split(\"-\");\n                                                                                                if (day === \"\" || parseInt(day) >= 1 && parseInt(day) <= 31) {\n                                                                                                    const newDate = \"\".concat(parts[0] || \"----\", \"-\").concat(parts[1] || \"--\", \"-\").concat(day.padStart(2, \"0\"));\n                                                                                                    setDateRange((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            start: day === \"\" ? \"\".concat(parts[0] || \"\", \"-\").concat(parts[1] || \"\").replace(/-+$/, \"\") : newDate\n                                                                                                        }));\n                                                                                                }\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 859,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 820,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 818,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium mb-2 block\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 880,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                            placeholder: \"年\",\n                                                                                            maxLength: 4,\n                                                                                            className: \"w-16 text-center\",\n                                                                                            value: dateRange.end ? dateRange.end.split(\"-\")[0] : \"\",\n                                                                                            onChange: (e)=>{\n                                                                                                const year = e.target.value.replace(/\\D/g, \"\").slice(0, 4);\n                                                                                                const currentEnd = dateRange.end || \"----\";\n                                                                                                const parts = currentEnd.split(\"-\");\n                                                                                                const newDate = \"\".concat(year.padEnd(4, \"-\"), \"-\").concat(parts[1] || \"--\", \"-\").concat(parts[2] || \"--\");\n                                                                                                if (year.length === 4 || year === \"\") {\n                                                                                                    setDateRange((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            end: year === \"\" ? \"\" : newDate.replace(/-+$/, \"\")\n                                                                                                        }));\n                                                                                                }\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 882,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"/\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 900,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                            placeholder: \"月\",\n                                                                                            maxLength: 2,\n                                                                                            className: \"w-12 text-center\",\n                                                                                            value: dateRange.end ? dateRange.end.split(\"-\")[1] || \"\" : \"\",\n                                                                                            onChange: (e)=>{\n                                                                                                const month = e.target.value.replace(/\\D/g, \"\").slice(0, 2);\n                                                                                                const currentEnd = dateRange.end || \"----\";\n                                                                                                const parts = currentEnd.split(\"-\");\n                                                                                                if (month === \"\" || parseInt(month) >= 1 && parseInt(month) <= 12) {\n                                                                                                    const newDate = \"\".concat(parts[0] || \"----\", \"-\").concat(month.padStart(2, \"0\"), \"-\").concat(parts[2] || \"--\");\n                                                                                                    setDateRange((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            end: month === \"\" ? parts[0] || \"\" : newDate.replace(/-+$/, \"\")\n                                                                                                        }));\n                                                                                                }\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 901,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"/\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 919,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                            placeholder: \"日\",\n                                                                                            maxLength: 2,\n                                                                                            className: \"w-12 text-center\",\n                                                                                            value: dateRange.end ? dateRange.end.split(\"-\")[2] || \"\" : \"\",\n                                                                                            onChange: (e)=>{\n                                                                                                const day = e.target.value.replace(/\\D/g, \"\").slice(0, 2);\n                                                                                                const currentEnd = dateRange.end || \"----\";\n                                                                                                const parts = currentEnd.split(\"-\");\n                                                                                                if (day === \"\" || parseInt(day) >= 1 && parseInt(day) <= 31) {\n                                                                                                    const newDate = \"\".concat(parts[0] || \"----\", \"-\").concat(parts[1] || \"--\", \"-\").concat(day.padStart(2, \"0\"));\n                                                                                                    setDateRange((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            end: day === \"\" ? \"\".concat(parts[0] || \"\", \"-\").concat(parts[1] || \"\").replace(/-+$/, \"\") : newDate\n                                                                                                        }));\n                                                                                                }\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 920,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 881,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 879,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 968,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 967,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 975,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 976,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 977,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 979,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 983,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 984,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 990,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 988,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1008,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1012,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1011,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1020,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1024,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1040,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1046,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1060,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1063,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1066,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1077,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1080,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1083,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1101,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1109,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1108,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1114,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1113,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1118,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1117,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1121,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1124,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1123,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1130,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1129,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1137,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1136,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1141,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1140,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1147,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1146,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1150,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1155,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1151,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1112,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1106,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 972,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1190,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1202,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 673,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 672,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1217,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1229,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.BatchTranslationProgress, {\n                open: batchTranslationProgress.isOpen,\n                onOpenChange: batchTranslationProgress.setIsOpen,\n                items: batchTranslationProgress.items,\n                isProcessing: batchTranslationProgress.isProcessing,\n                onCancel: batchTranslationProgress.cancelTranslation,\n                onComplete: async ()=>{\n                    // 翻译完成后刷新数据\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    // 延迟关闭对话框，让用户看到完成状态\n                    setTimeout(()=>{\n                        batchTranslationProgress.closeDialog();\n                    }, 2000);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 671,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"dWHnEtktIVXSiFxIRpDIyJSCiHE=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});