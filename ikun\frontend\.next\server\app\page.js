/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cproviders.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cproviders.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1QyVFNSU5QyVBOCVFNCVCQSU5MSVFNyVBQiVBRiVFNSVCNyVBNSVFNCVCRCU5QyVFOCVBRSVCMCVFNSVCRCU5NSU1Q0dpdGh1YiU1Q2lrdW5fZXJwJTVDaWt1biU1Q2Zyb250ZW5kJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9RCUzQSU1QyVFNSU5QyVBOCVFNCVCQSU5MSVFNyVBQiVBRiVFNSVCNyVBNSVFNCVCRCU5QyVFOCVBRSVCMCVFNSVCRCU5NSU1Q0dpdGh1YiU1Q2lrdW5fZXJwJTVDaWt1biU1Q2Zyb250ZW5kJTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1EJTNBJTVDJUU1JTlDJUE4JUU0JUJBJTkxJUU3JUFCJUFGJUU1JUI3JUE1JUU0JUJEJTlDJUU4JUFFJUIwJUU1JUJEJTk1JTVDR2l0aHViJTVDaWt1bl9lcnAlNUNpa3VuJTVDZnJvbnRlbmQlNUNzcmMlNUNjb21wb25lbnRzJTVDcHJvdmlkZXJzLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pa3VuLWVycC1mcm9udGVuZC8/NDVkMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXOWcqOS6keerr+W3peS9nOiusOW9lVxcXFxHaXRodWJcXFxcaWt1bl9lcnBcXFxcaWt1blxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cauth%5Cauth-guard.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cdashboard-stats.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cproduct-overview.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cquick-actions.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Crecent-tasks.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Clayout%5Cdashboard-layout.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cauth%5Cauth-guard.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cdashboard-stats.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cproduct-overview.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cquick-actions.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Crecent-tasks.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Clayout%5Cdashboard-layout.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/auth-guard.tsx */ \"(ssr)/./src/components/auth/auth-guard.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/dashboard-stats.tsx */ \"(ssr)/./src/components/dashboard/dashboard-stats.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/product-overview.tsx */ \"(ssr)/./src/components/dashboard/product-overview.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/quick-actions.tsx */ \"(ssr)/./src/components/dashboard/quick-actions.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/recent-tasks.tsx */ \"(ssr)/./src/components/dashboard/recent-tasks.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/dashboard-layout.tsx */ \"(ssr)/./src/components/layout/dashboard-layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cauth%5Cauth-guard.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cdashboard-stats.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cproduct-overview.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Cquick-actions.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Cdashboard%5Crecent-tasks.tsx&modules=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Ccomponents%5Clayout%5Cdashboard-layout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/auth-guard.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/auth-guard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard auto */ \n\n\n\nfunction AuthGuard({ children, requireAuth = true, requiredRole }) {\n    const { isAuthenticated, isLoading, user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) return;\n        // 需要认证但未登录\n        if (requireAuth && !isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        // 需要特定角色但权限不足\n        if (requireAuth && isAuthenticated && requiredRole && user) {\n            const roleHierarchy = {\n                \"viewer\": 1,\n                \"user\": 2,\n                \"admin\": 3\n            };\n            const userLevel = roleHierarchy[user.role] || 0;\n            const requiredLevel = roleHierarchy[requiredRole] || 0;\n            if (userLevel < requiredLevel) {\n                router.push(\"/unauthorized\");\n                return;\n            }\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        user,\n        requireAuth,\n        requiredRole,\n        router\n    ]);\n    // 显示加载状态\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-guard.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-guard.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-guard.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-guard.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    // 需要认证但未登录\n    if (requireAuth && !isAuthenticated) {\n        return null // 会被重定向到登录页\n        ;\n    }\n    // 权限不足\n    if (requireAuth && isAuthenticated && requiredRole && user) {\n        const roleHierarchy = {\n            \"viewer\": 1,\n            \"user\": 2,\n            \"admin\": 3\n        };\n        const userLevel = roleHierarchy[user.role] || 0;\n        const requiredLevel = roleHierarchy[requiredRole] || 0;\n        if (userLevel < requiredLevel) {\n            return null // 会被重定向到未授权页面\n            ;\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/auth-guard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/dashboard-stats.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/dashboard-stats.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStats: () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Package,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Package,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Package,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Package,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardStats auto */ \n\n\n\n// 模拟数据，实际应该从API获取\nconst stats = [\n    {\n        title: \"总产品数\",\n        value: 1234,\n        change: \"+12%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        title: \"活跃产品\",\n        value: 856,\n        change: \"+8%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        title: \"待处理订单\",\n        value: 23,\n        change: \"-5%\",\n        changeType: \"negative\",\n        icon: _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        title: \"店铺数量\",\n        value: 5,\n        change: \"+1\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_Package_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    }\n];\nfunction DashboardStats() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n        children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: stat.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                className: \"h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(stat.value)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-xs ${stat.changeType === \"positive\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                children: [\n                                    stat.change,\n                                    \" 较上月\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, stat.title, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/dashboard-stats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/product-overview.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/product-overview.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductOverview: () => (/* binding */ ProductOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductOverview auto */ \n\n\n\n// 模拟数据\nconst recentProducts = [\n    {\n        id: \"1\",\n        sku: \"SKU001\",\n        title: \"无线蓝牙耳机 - 高音质降噪\",\n        status: \"active\",\n        price: 299.99,\n        createdAt: \"2024-01-15T10:30:00Z\"\n    },\n    {\n        id: \"2\",\n        sku: \"SKU002\",\n        title: \"智能手表 - 健康监测\",\n        status: \"pending\",\n        price: 899.99,\n        createdAt: \"2024-01-14T15:20:00Z\"\n    },\n    {\n        id: \"3\",\n        sku: \"SKU003\",\n        title: \"便携式充电宝 - 20000mAh\",\n        status: \"active\",\n        price: 159.99,\n        createdAt: \"2024-01-13T09:15:00Z\"\n    },\n    {\n        id: \"4\",\n        sku: \"SKU004\",\n        title: \"无线充电器 - 快充版\",\n        status: \"draft\",\n        price: 89.99,\n        createdAt: \"2024-01-12T14:45:00Z\"\n    },\n    {\n        id: \"5\",\n        sku: \"SKU005\",\n        title: \"蓝牙音箱 - 防水便携\",\n        status: \"active\",\n        price: 199.99,\n        createdAt: \"2024-01-11T11:30:00Z\"\n    }\n];\nfunction ProductOverview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        children: \"最新产品\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                        children: \"最近添加的产品列表\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: recentProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium truncate\",\n                                                children: product.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                variant: \"outline\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getStatusColor)(product.status),\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getStatusText)(product.status)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-xs text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"SKU: \",\n                                                    product.sku\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"\\xa5\",\n                                                    product.price\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(product.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this)\n                        }, product.id, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\product-overview.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/product-overview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/quick-actions.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/quick-actions.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: () => (/* binding */ QuickActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Plus,Settings,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Plus,Settings,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Plus,Settings,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Plus,Settings,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ QuickActions auto */ \n\n\n\n\nconst quickActions = [\n    {\n        title: \"添加产品\",\n        description: \"手动添加新产品\",\n        href: \"/products/new\",\n        icon: _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        variant: \"default\"\n    },\n    {\n        title: \"产品采集\",\n        description: \"从平台采集产品\",\n        href: \"/scraping\",\n        icon: _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        variant: \"secondary\"\n    },\n    {\n        title: \"批量上架\",\n        description: \"发布产品到平台\",\n        href: \"/publishing\",\n        icon: _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        variant: \"secondary\"\n    },\n    {\n        title: \"系统设置\",\n        description: \"配置系统参数\",\n        href: \"/system/settings\",\n        icon: _barrel_optimize_names_Download_Plus_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        variant: \"outline\"\n    }\n];\nfunction QuickActions() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        children: \"快速操作\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"常用功能快捷入口\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-3\",\n                    children: quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: action.variant,\n                            className: \"h-auto p-4 justify-start\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: action.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: action.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: action.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this)\n                        }, action.title, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/quick-actions.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/recent-tasks.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/recent-tasks.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentTasks: () => (/* binding */ RecentTasks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentTasks auto */ \n\n\n\n\n// 模拟数据\nconst recentTasks = [\n    {\n        id: \"1\",\n        type: \"translation\",\n        storeName: \"亚马逊美国店\",\n        status: \"running\",\n        progress: 65,\n        totalItems: 100,\n        processedItems: 65,\n        createdAt: \"2024-01-15T10:30:00Z\"\n    },\n    {\n        id: \"2\",\n        type: \"publish\",\n        storeName: \"eBay英国店\",\n        status: \"completed\",\n        progress: 100,\n        totalItems: 50,\n        processedItems: 50,\n        createdAt: \"2024-01-14T15:20:00Z\"\n    },\n    {\n        id: \"3\",\n        type: \"scraping\",\n        storeName: \"Shopify店铺\",\n        status: \"pending\",\n        progress: 0,\n        totalItems: 200,\n        processedItems: 0,\n        createdAt: \"2024-01-13T09:15:00Z\"\n    }\n];\nconst taskTypeMap = {\n    translation: \"翻译任务\",\n    publish: \"发布任务\",\n    scraping: \"采集任务\"\n};\nfunction RecentTasks() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        children: \"最近任务\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                        children: \"任务执行状态和进度\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: recentTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium\",\n                                                children: taskTypeMap[task.type]\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                variant: \"outline\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getStatusColor)(task.status),\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getStatusText)(task.status)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mb-2\",\n                                        children: [\n                                            task.storeName,\n                                            \" • \",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.createdAt)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_3__.Progress, {\n                                                value: task.progress,\n                                                className: \"flex-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    task.processedItems,\n                                                    \"/\",\n                                                    task.totalItems\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this)\n                        }, task.id, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\dashboard\\\\recent-tasks.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/recent-tasks.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/dashboard-layout.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/dashboard-layout.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _top_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./top-navigation */ \"(ssr)/./src/components/layout/top-navigation.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\nfunction DashboardLayout({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_top_navigation__WEBPACK_IMPORTED_MODULE_1__.TopNavigation, {}, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full p-4\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvZGFzaGJvYXJkLWxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBR2dEO0FBQ2hCO0FBT3pCLFNBQVNFLGdCQUFnQixFQUFFQyxRQUFRLEVBQUVDLFNBQVMsRUFBd0I7SUFDM0UscUJBQ0UsOERBQUNDO1FBQUlELFdBQVU7OzBCQUViLDhEQUFDSiwwREFBYUE7Ozs7OzBCQUdkLDhEQUFDTTtnQkFBS0YsV0FBV0gsOENBQUVBLENBQUMsVUFBVUc7MEJBQzVCLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDWkQ7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pa3VuLWVycC1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL2xheW91dC9kYXNoYm9hcmQtbGF5b3V0LnRzeD8yYzM3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRvcE5hdmlnYXRpb24gfSBmcm9tICcuL3RvcC1uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscydcblxuaW50ZXJmYWNlIERhc2hib2FyZExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7IGNoaWxkcmVuLCBjbGFzc05hbWUgfTogRGFzaGJvYXJkTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICB7Lyog6aG26YOo5a+86IiqICovfVxuICAgICAgPFRvcE5hdmlnYXRpb24gLz5cblxuICAgICAgey8qIOS4u+WGheWuueWMuuWfnyAqL31cbiAgICAgIDxtYWluIGNsYXNzTmFtZT17Y24oXCJmbGV4LTFcIiwgY2xhc3NOYW1lKX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIHAtNFwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJUb3BOYXZpZ2F0aW9uIiwiY24iLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImRpdiIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/top-navigation.tsx":
/*!**************************************************!*\
  !*** ./src/components/layout/top-navigation.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TopNavigation: () => (/* binding */ TopNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Monitor,Moon,Package,Settings,ShoppingCart,Store,Sun,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ TopNavigation auto */ \n\n\n\n\n\n\n\n\n// 定义导航结构\nconst navigationConfig = [\n    {\n        id: \"home\",\n        label: \"首页\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-blue-500\",\n        directPath: \"/\",\n        categories: []\n    },\n    {\n        id: \"products\",\n        label: \"产品管理\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-blue-500\",\n        categories: [\n            {\n                id: \"scraping\",\n                label: \"采集\",\n                items: [\n                    {\n                        id: \"scraping\",\n                        label: \"采集箱\",\n                        path: \"/scraping\"\n                    }\n                ]\n            },\n            {\n                id: \"catalog\",\n                label: \"目录管理\",\n                items: [\n                    {\n                        id: \"categories-list\",\n                        label: \"本地目录\",\n                        path: \"/products/catalog\"\n                    },\n                    {\n                        id: \"mapping-management\",\n                        label: \"映射管理\",\n                        path: \"/products/mapping\"\n                    }\n                ]\n            },\n            {\n                id: \"dropship\",\n                label: \"产品管理\",\n                items: [\n                    {\n                        id: \"dropship-list\",\n                        label: \"铺货产品\",\n                        path: \"/products/dropship\"\n                    },\n                    {\n                        id: \"pod-list\",\n                        label: \"POD产品\",\n                        path: \"/products/pod\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"upload\",\n        label: \"刊登在线\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-green-500\",\n        categories: [\n            {\n                id: \"wortenmain\",\n                label: \"Worten\",\n                items: [\n                    {\n                        id: \"worten\",\n                        label: \"Worten刊登\",\n                        path: \"/uploadproduct/worten/listing\"\n                    },\n                    {\n                        id: \"wortenonly\",\n                        label: \"在线同步\",\n                        path: \"/uploadproduct/worten/online\"\n                    }\n                ]\n            },\n            {\n                id: \"phhmain\",\n                label: \"PHH\",\n                items: [\n                    {\n                        id: \"phh\",\n                        label: \"PHH刊登\",\n                        path: \"/uploadproduct/phh/listing\"\n                    },\n                    {\n                        id: \"phhonly\",\n                        label: \"在线同步\",\n                        path: \"/uploadproduct/phh/online\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"orders\",\n        label: \"订单管理\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-orange-500\",\n        categories: [\n            {\n                id: \"order-processing\",\n                label: \"订单处理\",\n                items: [\n                    {\n                        id: \"orders-list\",\n                        label: \"订单列表\",\n                        path: \"/orders\"\n                    },\n                    {\n                        id: \"fulfillment\",\n                        label: \"订单履行\",\n                        path: \"/orders/fulfillment\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"stores\",\n        label: \"店铺管理\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-purple-500\",\n        categories: [\n            {\n                id: \"store-config\",\n                label: \"店铺配置\",\n                items: [\n                    {\n                        id: \"stores-list\",\n                        label: \"店铺列表\",\n                        path: \"/stores\"\n                    },\n                    {\n                        id: \"platforms\",\n                        label: \"平台配置\",\n                        path: \"/stores/platforms\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"analytics\",\n        label: \"数据分析\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-indigo-500\",\n        categories: [\n            {\n                id: \"reports\",\n                label: \"报表分析\",\n                items: [\n                    {\n                        id: \"dashboard\",\n                        label: \"数据看板\",\n                        path: \"/dashboard\"\n                    },\n                    {\n                        id: \"sales\",\n                        label: \"销售分析\",\n                        path: \"/analytics/sales\"\n                    }\n                ]\n            }\n        ]\n    },\n    //任务中心\n    {\n        id: \"tasks\",\n        label: \"任务中心\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-pink-500\",\n        categories: [\n            {\n                id: \"translation\",\n                label: \"翻译任务\",\n                items: [\n                    {\n                        id: \"translation-list\",\n                        label: \"翻译列表\",\n                        path: \"/tasks/translation\"\n                    }\n                ]\n            },\n            {\n                id: \"pod\",\n                label: \"POD任务\",\n                items: [\n                    {\n                        id: \"pod-list\",\n                        label: \"POD列表\",\n                        path: \"/tasks/pod\"\n                    }\n                ]\n            },\n            {\n                id: \"scheduled\",\n                label: \"定时任务\",\n                items: [\n                    {\n                        id: \"scheduled-list\",\n                        label: \"定时列表\",\n                        path: \"/tasks/scheduled\"\n                    }\n                ]\n            }\n        ]\n    },\n    //系统设置\n    {\n        id: \"system\",\n        label: \"系统设置\",\n        icon: _barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-gray-500\",\n        categories: [\n            {\n                id: \"settings\",\n                label: \"系统配置\",\n                items: [\n                    {\n                        id: \"users\",\n                        label: \"用户管理\",\n                        path: \"/system/users\"\n                    },\n                    {\n                        id: \"settings\",\n                        label: \"系统设置\",\n                        path: \"/system/settings\"\n                    }\n                ]\n            }\n        ]\n    }\n];\nfunction TopNavigation({ className }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const { user, logout } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [hoveredTab, setHoveredTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 监听路径变化，更新活跃的导航项\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 根据当前路径确定活跃的导航项\n        if (pathname === \"/\") {\n            setActiveTab(\"home\");\n        } else if (pathname.startsWith(\"/products\")) {\n            setActiveTab(\"products\");\n        } else if (pathname.startsWith(\"/uploadproduct\")) {\n            setActiveTab(\"upload\");\n        } else if (pathname.startsWith(\"/stores\")) {\n            setActiveTab(\"stores\");\n        } else if (pathname.startsWith(\"/orders\")) {\n            setActiveTab(\"orders\");\n        } else if (pathname.startsWith(\"/analytics\") || pathname.startsWith(\"/dashboard\")) {\n            setActiveTab(\"analytics\");\n        } else if (pathname.startsWith(\"/tasks\")) {\n            setActiveTab(\"tasks\");\n        } else if (pathname.startsWith(\"/system\")) {\n            setActiveTab(\"system\");\n        }\n    }, [\n        pathname\n    ]);\n    // 处理退出登录\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"border-b bg-background\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 md:px-6 py-3 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 md:space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-9 h-9 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"IK\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-lg md:text-xl text-gray-900 dark:text-white\",\n                                        children: \"IKUN ERP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"md:hidden p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex items-center space-x-2\",\n                                children: navigationConfig.map((nav)=>{\n                                    const Icon = nav.icon;\n                                    const isActive = activeTab === nav.id;\n                                    const isHovered = hoveredTab === nav.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>{\n                                            if (nav.categories.length > 0) {\n                                                setHoveredTab(nav.id);\n                                            }\n                                        },\n                                        onMouseLeave: ()=>setHoveredTab(null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (nav.directPath) {\n                                                        setHoveredTab(null);\n                                                        router.push(nav.directPath);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center space-x-2 px-3 md:px-4 py-2 rounded-lg transition-all duration-200 font-medium\", isActive ? \"bg-blue-600 text-white shadow-sm\" : \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: nav.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this),\n                                            isHovered && nav.categories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 shadow-lg z-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 min-w-[300px] md:min-w-[450px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-5\",\n                                                        children: nav.categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 md:w-20 flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-semibold text-gray-900 dark:text-gray-100 py-1\",\n                                                                            children: category.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 grid grid-cols-2 md:grid-cols-3 gap-x-4 md:gap-x-6 gap-y-2\",\n                                                                        children: category.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                                                href: item.path,\n                                                                                onClick: ()=>setHoveredTab(null),\n                                                                                className: \"text-left text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors whitespace-nowrap py-1 h-7 flex items-center\",\n                                                                                children: item.label\n                                                                            }, item.id, false, {\n                                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                                                lineNumber: 310,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, category.id, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, nav.id, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                            children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this) : theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-32\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"light\"),\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"浅色模式\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"dark\"),\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"深色模式\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"system\"),\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"跟随系统\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center space-x-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm hidden md:inline\",\n                                                    children: user?.username || \"用户\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Monitor_Moon_Package_Settings_ShoppingCart_Store_Sun_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-48\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                onClick: ()=>router.push(\"/profile\"),\n                                                children: \"个人设置\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                onClick: handleLogout,\n                                                className: \"text-red-600 dark:text-red-400\",\n                                                children: \"退出登录\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-3 space-y-3\",\n                    children: navigationConfig.map((nav)=>{\n                        const Icon = nav.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: nav.directPath ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setMobileMenuOpen(false);\n                                    router.push(nav.directPath);\n                                },\n                                className: \"flex items-center space-x-3 w-full px-3 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: nav.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 21\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 px-3 py-2 text-gray-900 dark:text-gray-100 font-semibold\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: nav.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-8 space-y-1\",\n                                        children: nav.categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-3 py-1 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: category.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: category.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                                href: item.path,\n                                                                onClick: ()=>setMobileMenuOpen(false),\n                                                                className: \"block w-full text-left px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors\",\n                                                                children: item.label\n                                                            }, item.id, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 33\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, category.id, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 27\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 21\n                            }, this)\n                        }, nav.id, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\layout\\\\top-navigation.tsx\",\n        lineNumber: 241,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/top-navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(ssr)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/toaster */ \"(ssr)/./src/components/ui/toaster.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    refetchOnWindowFocus: false\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n            attribute: \"class\",\n            defaultTheme: \"system\",\n            enableSystem: true,\n            disableTransitionOnChange: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_3__.GlobalConfirmProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFFRTtBQUVoQyxNQUFNRSxxQkFBT0YsNkNBQWdCLENBRzNCLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCw0REFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsS0FBS00sV0FBVyxHQUFHO0FBRW5CLE1BQU1DLDJCQUFhVCw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRSwwQkFBWVYsNkNBQWdCLENBR2hDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDSztRQUNDTCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCxzREFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkssVUFBVUYsV0FBVyxHQUFHO0FBRXhCLE1BQU1JLGdDQUFrQlosNkNBQWdCLENBR3RDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTztRQUNDUCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FBQyxpQ0FBaUNHO1FBQzlDLEdBQUdDLEtBQUs7Ozs7OztBQUdiTyxnQkFBZ0JKLFdBQVcsR0FBRztBQUU5QixNQUFNTSw0QkFBY2QsNkNBQWdCLENBR2xDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxZQUFZRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVoRVMsWUFBWU4sV0FBVyxHQUFHO0FBRTFCLE1BQU1PLDJCQUFhZiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLDhCQUE4Qkc7UUFDM0MsR0FBR0MsS0FBSzs7Ozs7O0FBR2JVLFdBQVdQLFdBQVcsR0FBRztBQUV1RCIsInNvdXJjZXMiOlsid2VicGFjazovL2lrdW4tZXJwLWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvdWkvY2FyZC50c3g/ZTdkMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IENhcmQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJyb3VuZGVkLWxnIGJvcmRlciBiZy1jYXJkIHRleHQtY2FyZC1mb3JlZ3JvdW5kIHNoYWRvdy1zbVwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZC5kaXNwbGF5TmFtZSA9IFwiQ2FyZFwiXG5cbmNvbnN0IENhcmRIZWFkZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTEuNSBwLTZcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZEhlYWRlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEhlYWRlclwiXG5cbmNvbnN0IENhcmRUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxQYXJhZ3JhcGhFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MSGVhZGluZ0VsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxoM1xuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbGVhZGluZy1ub25lIHRyYWNraW5nLXRpZ2h0XCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkVGl0bGUuZGlzcGxheU5hbWUgPSBcIkNhcmRUaXRsZVwiXG5cbmNvbnN0IENhcmREZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxQYXJhZ3JhcGhFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MUGFyYWdyYXBoRWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPHBcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZERlc2NyaXB0aW9uLmRpc3BsYXlOYW1lID0gXCJDYXJkRGVzY3JpcHRpb25cIlxuXG5jb25zdCBDYXJkQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdiByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcInAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbikpXG5DYXJkQ29udGVudC5kaXNwbGF5TmFtZSA9IFwiQ2FyZENvbnRlbnRcIlxuXG5jb25zdCBDYXJkRm9vdGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRGb290ZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRGb290ZXJcIlxuXG5leHBvcnQgeyBDYXJkLCBDYXJkSGVhZGVyLCBDYXJkRm9vdGVyLCBDYXJkVGl0bGUsIENhcmREZXNjcmlwdGlvbiwgQ2FyZENvbnRlbnQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJDYXJkIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiZGl2IiwiZGlzcGxheU5hbWUiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiaDMiLCJDYXJkRGVzY3JpcHRpb24iLCJwIiwiQ2FyZENvbnRlbnQiLCJDYXJkRm9vdGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/confirm-dialog.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/confirm-dialog.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfirmDialog: () => (/* binding */ ConfirmDialog),\n/* harmony export */   GlobalConfirmProvider: () => (/* binding */ GlobalConfirmProvider),\n/* harmony export */   useConfirm: () => (/* binding */ useConfirm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,HelpCircle,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,HelpCircle,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,HelpCircle,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,HelpCircle,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ ConfirmDialog,useConfirm,GlobalConfirmProvider auto */ \n\n\n\n\nconst variantConfig = {\n    default: {\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        iconColor: \"text-blue-500\",\n        confirmVariant: \"default\"\n    },\n    destructive: {\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        iconColor: \"text-red-500\",\n        confirmVariant: \"destructive\"\n    },\n    warning: {\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        iconColor: \"text-yellow-500\",\n        confirmVariant: \"default\"\n    },\n    info: {\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        iconColor: \"text-blue-500\",\n        confirmVariant: \"default\"\n    },\n    success: {\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        iconColor: \"text-green-500\",\n        confirmVariant: \"default\"\n    }\n};\nfunction ConfirmDialog({ open, onOpenChange, onConfirm, onCancel, options = {} }) {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { title = \"确认操作\", description = \"您确定要执行此操作吗？\", confirmText = \"确认\", cancelText = \"取消\", variant = \"default\", icon = true } = options;\n    const config = variantConfig[variant];\n    const IconComponent = config.icon;\n    const handleConfirm = async ()=>{\n        setLoading(true);\n        try {\n            await onConfirm();\n            onOpenChange(false);\n        } catch (error) {\n            console.error(\"确认操作失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        if (onCancel) {\n            onCancel();\n        }\n        onOpenChange(false);\n    };\n    // 重置loading状态当对话框关闭时\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!open) {\n            setLoading(false);\n        }\n    }, [\n        open\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: `w-5 h-5 ${config.iconColor}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                title\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            className: \"text-left\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                    className: \"flex-row justify-end gap-2 sm:gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: handleCancel,\n                            disabled: loading,\n                            children: cancelText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: config.confirmVariant,\n                            onClick: handleConfirm,\n                            disabled: loading,\n                            children: loading ? \"处理中...\" : confirmText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\nlet globalConfirmState = {\n    open: false\n};\nlet setGlobalConfirmState = null;\nfunction useConfirm() {\n    const confirm = (options)=>{\n        return new Promise((resolve)=>{\n            if (setGlobalConfirmState) {\n                setGlobalConfirmState({\n                    open: true,\n                    resolve,\n                    options\n                });\n            } else {\n                // 如果没有全局状态管理器，回退到原生confirm\n                resolve(window.confirm(options?.description || \"您确定要执行此操作吗？\"));\n            }\n        });\n    };\n    return {\n        confirm\n    };\n}\n// 全局确认对话框提供者组件\nfunction GlobalConfirmProvider({ children }) {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false\n    });\n    // 注册全局状态设置器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setGlobalConfirmState = setState;\n        return ()=>{\n            setGlobalConfirmState = null;\n        };\n    }, []);\n    const handleConfirm = ()=>{\n        if (state.resolve) {\n            state.resolve(true);\n        }\n        setState({\n            open: false\n        });\n    };\n    const handleCancel = ()=>{\n        if (state.resolve) {\n            state.resolve(false);\n        }\n        setState({\n            open: false\n        });\n    };\n    const handleOpenChange = (open)=>{\n        if (!open && state.resolve) {\n            state.resolve(false);\n        }\n        setState((prev)=>({\n                ...prev,\n                open\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConfirmDialog, {\n                open: state.open,\n                onOpenChange: handleOpenChange,\n                onConfirm: handleConfirm,\n                onCancel: handleCancel,\n                options: state.options\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\confirm-dialog.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/confirm-dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dropdown-menu.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/dropdown-menu.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/progress.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/progress.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-progress */ \"(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Progress auto */ \n\n\n\nconst Progress = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, value, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: \"h-full w-full flex-1 bg-primary transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nProgress.displayName = _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-4 left-1/2 transform -translate-x-1/2 z-[100] flex max-h-screen w-full max-w-sm flex-col p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-center overflow-hidden rounded-full border-0 px-6 py-2 shadow-md transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-top-full data-[state=open]:slide-in-from-top-full\", {\n    variants: {\n        variant: {\n            default: \"bg-green-500 text-white\",\n            destructive: \"bg-red-500 text-white\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 61,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"hidden\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 85,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 76,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 106,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, variant, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    variant: variant,\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center text-center w-full\",\n                            children: title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                className: \"text-sm font-medium whitespace-nowrap\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 17\n                            }, this) : description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                className: \"text-sm font-medium whitespace-nowrap\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 17\n                            }, this) : null\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action\n                    ]\n                }, id, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-toast.ts":
/*!********************************!*\
  !*** ./src/hooks/use-toast.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 5000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-api */ \"(ssr)/./src/lib/auth-api.ts\");\n/**\n * 认证Hook\n */ \n\nconst STORAGE_KEYS = {\n    TOKEN: \"auth_token\",\n    REFRESH_TOKEN: \"auth_refresh_token\",\n    USER: \"auth_user\"\n};\nfunction useAuth() {\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        user: null,\n        token: null,\n        refreshToken: null,\n        isAuthenticated: false,\n        isLoading: true\n    });\n    // 清除认证状态\n    const clearAuthState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (true) return;\n        localStorage.removeItem(STORAGE_KEYS.TOKEN);\n        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\n        localStorage.removeItem(STORAGE_KEYS.USER);\n    }, []);\n    // 保存认证状态到localStorage\n    const saveAuthState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((user, token, refreshToken)=>{\n        if (true) return;\n        localStorage.setItem(STORAGE_KEYS.TOKEN, token);\n        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);\n        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));\n    }, []);\n    // 从localStorage加载认证状态\n    const loadAuthState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (true) return;\n        try {\n            const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n            const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\n            const userStr = localStorage.getItem(STORAGE_KEYS.USER);\n            if (token && refreshToken && userStr) {\n                const user = JSON.parse(userStr);\n                // 验证token有效性\n                try {\n                    // 调用后端验证token\n                    await _lib_auth_api__WEBPACK_IMPORTED_MODULE_1__.authApi.getProfile();\n                    // Token有效，设置认证状态\n                    setAuthState({\n                        user,\n                        token,\n                        refreshToken,\n                        isAuthenticated: true,\n                        isLoading: false\n                    });\n                } catch (error) {\n                    console.warn(\"Token validation failed, clearing auth state:\", error);\n                    // Token无效，清除认证状态\n                    clearAuthState();\n                    setAuthState({\n                        user: null,\n                        token: null,\n                        refreshToken: null,\n                        isAuthenticated: false,\n                        isLoading: false\n                    });\n                }\n            } else {\n                setAuthState((prev)=>({\n                        ...prev,\n                        isLoading: false\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Failed to load auth state:\", error);\n            // 发生错误时也清除认证状态\n            clearAuthState();\n            setAuthState({\n                user: null,\n                token: null,\n                refreshToken: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        }\n    }, [\n        clearAuthState\n    ]);\n    // 登录\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (credentials)=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: true\n                }));\n            const response = await _lib_auth_api__WEBPACK_IMPORTED_MODULE_1__.authApi.login(credentials);\n            const { user, token, refreshToken } = response;\n            if (!user || !token || !refreshToken) {\n                console.error(\"Missing required fields in response:\", {\n                    user: !!user,\n                    token: !!token,\n                    refreshToken: !!refreshToken\n                });\n                throw new Error(\"Invalid response format\");\n            }\n            saveAuthState(user, token, refreshToken);\n            setAuthState({\n                user,\n                token,\n                refreshToken,\n                isAuthenticated: true,\n                isLoading: false\n            });\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Login failed\"\n            };\n        }\n    }, [\n        saveAuthState\n    ]);\n    // 登出\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            await _lib_auth_api__WEBPACK_IMPORTED_MODULE_1__.authApi.logout();\n        } catch (error) {\n            console.error(\"Logout API call failed:\", error);\n        } finally{\n            clearAuthState();\n            setAuthState({\n                user: null,\n                token: null,\n                refreshToken: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        }\n    }, [\n        clearAuthState\n    ]);\n    // 刷新token\n    const refreshToken = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!authState.refreshToken) return false;\n        try {\n            const response = await _lib_auth_api__WEBPACK_IMPORTED_MODULE_1__.authApi.refreshToken({\n                refreshToken: authState.refreshToken\n            });\n            const newToken = response.token;\n            if (false) {}\n            setAuthState((prev)=>({\n                    ...prev,\n                    token: newToken\n                }));\n            return true;\n        } catch (error) {\n            console.error(\"Token refresh failed:\", error);\n            await logout();\n            return false;\n        }\n    }, [\n        authState.refreshToken,\n        logout\n    ]);\n    // 获取用户信息\n    const fetchProfile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!authState.isAuthenticated) return;\n        try {\n            const user = await _lib_auth_api__WEBPACK_IMPORTED_MODULE_1__.authApi.getProfile();\n            if (false) {}\n            setAuthState((prev)=>({\n                    ...prev,\n                    user\n                }));\n        } catch (error) {\n            console.error(\"Failed to fetch profile:\", error);\n            // 如果获取用户信息失败，可能token已过期\n            await logout();\n        }\n    }, [\n        authState.isAuthenticated,\n        logout\n    ]);\n    // 初始化时加载认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        loadAuthState().catch((error)=>{\n            console.error(\"Failed to initialize auth state:\", error);\n        });\n    }, [\n        loadAuthState\n    ]);\n    return {\n        ...authState,\n        login,\n        logout,\n        refreshToken,\n        fetchProfile\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   categoriesApi: () => (/* binding */ categoriesApi),\n/* harmony export */   productsApi: () => (/* binding */ productsApi)\n/* harmony export */ });\n/**\n * API Client\n * 前端API调用客户端\n */ // API基础配置\nconst API_BASE_URL = \"http://localhost:3001/api/v1\" || 0;\n// API客户端类\nclass ApiClient {\n    constructor(baseURL){\n        this.baseURL = baseURL;\n    }\n    // 获取认证token\n    getAuthToken() {\n        if (false) {}\n        return null;\n    }\n    // 通用请求方法\n    async request(endpoint, config = {}) {\n        const { method = \"GET\", headers = {}, body } = config;\n        const url = `${this.baseURL}${endpoint}`;\n        const requestHeaders = {\n            \"Content-Type\": \"application/json\",\n            ...headers\n        };\n        // 添加认证token\n        const token = this.getAuthToken();\n        if (token) {\n            requestHeaders[\"Authorization\"] = `Bearer ${token}`;\n        }\n        const requestConfig = {\n            method,\n            headers: requestHeaders\n        };\n        if (body && method !== \"GET\") {\n            requestConfig.body = JSON.stringify(body);\n        }\n        try {\n            const response = await fetch(url, requestConfig);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                console.error(\"HTTP Error:\", response.status, response.statusText, errorData);\n                // 处理401未授权错误\n                if (response.status === 401) {\n                    // 清除本地存储的认证信息\n                    if (false) {}\n                    // 如果不是登录页面，重定向到登录页面\n                    if (false) {}\n                }\n                // 创建包含完整错误数据的错误对象\n                const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\n                error.response = {\n                    data: errorData\n                };\n                error.status = response.status;\n                throw error;\n            }\n            const data = await response.json();\n            // 检查业务状态码 (200-299 都是成功状态码)\n            if (data.code < 200 || data.code >= 300) {\n                console.error(\"Business Logic Error:\", data);\n                throw new Error(data.message || \"Request failed\");\n            }\n            // 对于某些操作（如删除），我们需要返回完整的响应对象而不是嵌套的data字段\n            // 如果响应包含success字段，说明这是一个业务操作结果，返回完整对象\n            if (\"success\" in data) {\n                return data;\n            }\n            return data.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // GET请求\n    async get(endpoint, params) {\n        let url = endpoint;\n        if (params) {\n            const searchParams = new URLSearchParams();\n            Object.entries(params).forEach(([key, value])=>{\n                if (value !== undefined && value !== null && value !== \"\") {\n                    searchParams.append(key, String(value));\n                }\n            });\n            const queryString = searchParams.toString();\n            if (queryString) {\n                url += `?${queryString}`;\n            }\n        }\n        return this.request(url, {\n            method: \"GET\"\n        });\n    }\n    // POST请求\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"POST\",\n            body: data\n        });\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"PUT\",\n            body: data\n        });\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: \"DELETE\"\n        });\n    }\n}\n// 创建API客户端实例\nconst apiClient = new ApiClient(API_BASE_URL);\n// 产品相关API\nconst productsApi = {\n    // 获取产品列表\n    getProducts: (params)=>apiClient.get(\"/products\", params),\n    // 获取产品详情\n    getProduct: (id)=>apiClient.get(`/products/${id}`),\n    // 创建产品\n    createProduct: (data)=>apiClient.post(\"/products\", data),\n    // 更新产品\n    updateProduct: (id, data)=>apiClient.put(`/products/${id}`, data),\n    // 删除产品\n    deleteProduct: (id)=>apiClient.delete(`/products/${id}`),\n    // 批量导入产品\n    importProducts: (products)=>apiClient.post(\"/products/import\", {\n            products\n        }),\n    // 按分类获取产品\n    getProductsByCategory: (categoryId, params)=>apiClient.get(`/products/category/${categoryId}`, params)\n};\n// 分类相关API\nconst categoriesApi = {\n    // 获取分类列表\n    getCategories: (params)=>apiClient.get(\"/categories\", params),\n    // 获取分类树\n    getCategoryTree: ()=>apiClient.get(\"/categories/tree\"),\n    // 获取指定层级的分类\n    getCategoriesByLevel: (level)=>apiClient.get(`/categories/level/${level}`),\n    // 获取分类详情\n    getCategory: (id)=>apiClient.get(`/categories/${id}`),\n    // 创建分类\n    createCategory: (data)=>apiClient.post(\"/categories\", data),\n    // 更新分类\n    updateCategory: (id, data)=>apiClient.put(`/categories/${id}`, data),\n    // 更新分类状态\n    updateCategoryStatus: (id, status)=>apiClient.put(`/categories/${id}/status`, {\n            status\n        }),\n    // 删除分类\n    deleteCategory: (id)=>apiClient.delete(`/categories/${id}`),\n    // 获取子分类\n    getCategoryChildren: (id)=>apiClient.get(`/categories/${id}/children`),\n    // 批量创建分类\n    batchCreateCategories: (categories)=>apiClient.post(\"/categories/batch\", {\n            categories\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth-api.ts":
/*!*****************************!*\
  !*** ./src/lib/auth-api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/lib/api.ts\");\n/**\n * 认证相关API\n */ \nconst authApi = {\n    // 用户登录\n    login: async (credentials)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/auth/login\", credentials);\n        return response;\n    },\n    // 用户登出\n    logout: async ()=>{\n        await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/auth/logout\");\n    },\n    // 刷新token\n    refreshToken: async (refreshTokenData)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/auth/refresh\", refreshTokenData);\n        return response;\n    },\n    // 获取用户信息\n    getProfile: async ()=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/auth/profile\");\n        return response;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2F1dGgtYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7O0NBRUMsR0FFaUM7QUFHM0IsTUFBTUMsVUFBVTtJQUNyQixPQUFPO0lBQ1BDLE9BQU8sT0FBT0M7UUFDWixNQUFNQyxXQUFXLE1BQU1KLDJDQUFTQSxDQUFDSyxJQUFJLENBQWdCLGVBQWVGO1FBQ3BFLE9BQU9DO0lBQ1Q7SUFFQSxPQUFPO0lBQ1BFLFFBQVE7UUFDTixNQUFNTiwyQ0FBU0EsQ0FBQ0ssSUFBSSxDQUFPO0lBQzdCO0lBRUEsVUFBVTtJQUNWRSxjQUFjLE9BQU9DO1FBQ25CLE1BQU1KLFdBQVcsTUFBTUosMkNBQVNBLENBQUNLLElBQUksQ0FBdUIsaUJBQWlCRztRQUM3RSxPQUFPSjtJQUNUO0lBRUEsU0FBUztJQUNUSyxZQUFZO1FBQ1YsTUFBTUwsV0FBVyxNQUFNSiwyQ0FBU0EsQ0FBQ1UsR0FBRyxDQUFPO1FBQzNDLE9BQU9OO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaWt1bi1lcnAtZnJvbnRlbmQvLi9zcmMvbGliL2F1dGgtYXBpLnRzP2U1ZWUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiDorqTor4Hnm7jlhbNBUElcbiAqL1xuXG5pbXBvcnQgeyBhcGlDbGllbnQgfSBmcm9tICcuL2FwaSc7XG5pbXBvcnQgeyBMb2dpblJlcXVlc3QsIExvZ2luUmVzcG9uc2UsIFJlZnJlc2hUb2tlblJlcXVlc3QsIFJlZnJlc2hUb2tlblJlc3BvbnNlLCBVc2VyIH0gZnJvbSAnQC90eXBlcy9hdXRoJztcblxuZXhwb3J0IGNvbnN0IGF1dGhBcGkgPSB7XG4gIC8vIOeUqOaIt+eZu+W9lVxuICBsb2dpbjogYXN5bmMgKGNyZWRlbnRpYWxzOiBMb2dpblJlcXVlc3QpOiBQcm9taXNlPExvZ2luUmVzcG9uc2U+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0PExvZ2luUmVzcG9uc2U+KCcvYXV0aC9sb2dpbicsIGNyZWRlbnRpYWxzKTtcbiAgICByZXR1cm4gcmVzcG9uc2U7XG4gIH0sXG5cbiAgLy8g55So5oi355m75Ye6XG4gIGxvZ291dDogYXN5bmMgKCk6IFByb21pc2U8dm9pZD4gPT4ge1xuICAgIGF3YWl0IGFwaUNsaWVudC5wb3N0PG51bGw+KCcvYXV0aC9sb2dvdXQnKTtcbiAgfSxcblxuICAvLyDliLfmlrB0b2tlblxuICByZWZyZXNoVG9rZW46IGFzeW5jIChyZWZyZXNoVG9rZW5EYXRhOiBSZWZyZXNoVG9rZW5SZXF1ZXN0KTogUHJvbWlzZTxSZWZyZXNoVG9rZW5SZXNwb25zZT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3Q8UmVmcmVzaFRva2VuUmVzcG9uc2U+KCcvYXV0aC9yZWZyZXNoJywgcmVmcmVzaFRva2VuRGF0YSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9LFxuXG4gIC8vIOiOt+WPlueUqOaIt+S/oeaBr1xuICBnZXRQcm9maWxlOiBhc3luYyAoKTogUHJvbWlzZTxVc2VyPiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0PFVzZXI+KCcvYXV0aC9wcm9maWxlJyk7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbImFwaUNsaWVudCIsImF1dGhBcGkiLCJsb2dpbiIsImNyZWRlbnRpYWxzIiwicmVzcG9uc2UiLCJwb3N0IiwibG9nb3V0IiwicmVmcmVzaFRva2VuIiwicmVmcmVzaFRva2VuRGF0YSIsImdldFByb2ZpbGUiLCJnZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth-api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusText: () => (/* binding */ getStatusText),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"zh-CN\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction formatPrice(price) {\n    return new Intl.NumberFormat(\"zh-CN\", {\n        style: \"currency\",\n        currency: \"CNY\"\n    }).format(price);\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat(\"zh-CN\").format(num);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction getStatusColor(status) {\n    const statusColors = {\n        active: \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n        inactive: \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n        pending: \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300\",\n        running: \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\",\n        completed: \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n        failed: \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n        draft: \"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300\"\n    };\n    return statusColors[status] || statusColors.pending;\n}\nfunction getStatusText(status) {\n    const statusTexts = {\n        active: \"活跃\",\n        inactive: \"非活跃\",\n        pending: \"待处理\",\n        running: \"运行中\",\n        completed: \"已完成\",\n        failed: \"失败\",\n        draft: \"草稿\",\n        paid: \"已付款\",\n        shipped: \"已发货\",\n        delivered: \"已送达\",\n        cancelled: \"已取消\"\n    };\n    return statusTexts[status] || status;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"14cb4f7feada\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaWt1bi1lcnAtZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2Q0NDAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNGNiNGY3ZmVhZGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"IKUN ERP - 跨境电商管理系统\",\n    description: \"专业的跨境电商ERP管理系统，提供产品管理、采集、上架、订单管理等核心功能\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQzRCO0FBSTNDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsNERBQVNBOzBCQUNQSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaWt1bi1lcnAtZnJvbnRlbmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IFByb3ZpZGVycyB9IGZyb20gJ0AvY29tcG9uZW50cy9wcm92aWRlcnMnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdJS1VOIEVSUCAtIOi3qOWig+eUteWVhueuoeeQhuezu+e7nycsXG4gIGRlc2NyaXB0aW9uOiAn5LiT5Lia55qE6Leo5aKD55S15ZWGRVJQ566h55CG57O757uf77yM5o+Q5L6b5Lqn5ZOB566h55CG44CB6YeH6ZuG44CB5LiK5p6244CB6K6i5Y2V566h55CG562J5qC45b+D5Yqf6IO9Jyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxQcm92aWRlcnM+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1Byb3ZpZGVycz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlByb3ZpZGVycyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(rsc)/./src/components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/dashboard-stats */ \"(rsc)/./src/components/dashboard/dashboard-stats.tsx\");\n/* harmony import */ var _components_dashboard_product_overview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/product-overview */ \"(rsc)/./src/components/dashboard/product-overview.tsx\");\n/* harmony import */ var _components_dashboard_recent_tasks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/recent-tasks */ \"(rsc)/./src/components/dashboard/recent-tasks.tsx\");\n/* harmony import */ var _components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/quick-actions */ \"(rsc)/./src/components/dashboard/quick-actions.tsx\");\n/* harmony import */ var _components_auth_auth_guard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/auth/auth-guard */ \"(rsc)/./src/components/auth/auth-guard.tsx\");\n\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_guard__WEBPACK_IMPORTED_MODULE_6__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_1__.DashboardLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_2__.DashboardStats, {}, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_product_overview__WEBPACK_IMPORTED_MODULE_3__.ProductOverview, {}, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_5__.QuickActions, {}, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_tasks__WEBPACK_IMPORTED_MODULE_4__.RecentTasks, {}, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth/auth-guard.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/auth-guard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthGuard: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\auth\auth-guard.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\auth\auth-guard.tsx#AuthGuard`);


/***/ }),

/***/ "(rsc)/./src/components/dashboard/dashboard-stats.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/dashboard-stats.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardStats: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\dashboard-stats.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\dashboard-stats.tsx#DashboardStats`);


/***/ }),

/***/ "(rsc)/./src/components/dashboard/product-overview.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/product-overview.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProductOverview: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\product-overview.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\product-overview.tsx#ProductOverview`);


/***/ }),

/***/ "(rsc)/./src/components/dashboard/quick-actions.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/quick-actions.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuickActions: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\quick-actions.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\quick-actions.tsx#QuickActions`);


/***/ }),

/***/ "(rsc)/./src/components/dashboard/recent-tasks.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/recent-tasks.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RecentTasks: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\recent-tasks.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\dashboard\recent-tasks.tsx#RecentTasks`);


/***/ }),

/***/ "(rsc)/./src/components/layout/dashboard-layout.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/dashboard-layout.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardLayout: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\layout\dashboard-layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\layout\dashboard-layout.tsx#DashboardLayout`);


/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\在云端工作记录\Github\ikun_erp\ikun\frontend\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/@tanstack","vendor-chunks/lucide-react","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/next-themes","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/get-nonce","vendor-chunks/@floating-ui"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E5%9C%A8%E4%BA%91%E7%AB%AF%E5%B7%A5%E4%BD%9C%E8%AE%B0%E5%BD%95%5CGithub%5Cikun_erp%5Cikun%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();