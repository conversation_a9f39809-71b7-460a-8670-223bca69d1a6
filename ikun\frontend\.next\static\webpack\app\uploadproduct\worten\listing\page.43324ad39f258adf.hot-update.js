"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态 - 产品状态改为单选\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 单选状态\n    ;\n    const [selectedTranslationStatus, setSelectedTranslationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 翻译状态也改为单选\n    ;\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: undefined,\n        end: undefined\n    }) // 改回日期区间选择\n    ;\n    const [calendarOpen, setCalendarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 日历弹窗状态\n    ;\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数 - 改为单选模式\n    const handleStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleTranslationStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedTranslationStatus((prev)=>prev === status ? \"\" : status);\n    };\n    // 日历确认处理函数\n    const handleCalendarConfirm = ()=>{\n        setCalendarOpen(false) // 关闭日历弹窗\n        ;\n    // 筛选会通过useEffect自动触发\n    };\n    const handleReset = ()=>{\n        setSelectedStatus(\"\");\n        setSelectedTranslationStatus(\"\");\n        setDateRange({\n            start: undefined,\n            end: undefined\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatus) {\n            params.status = selectedStatus;\n        }\n        if (selectedTranslationStatus) {\n            params.translation_status = selectedTranslationStatus;\n        }\n        if (dateRange.start) {\n            const startDateStr = dateRange.start.toISOString().split(\"T\")[0];\n            params.start_date = startDateStr;\n        }\n        if (dateRange.end) {\n            const endDateStr = dateRange.end.toISOString().split(\"T\")[0];\n            params.end_date = endDateStr;\n        }\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatus,\n        selectedTranslationStatus,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 485,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 498,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 605,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 657,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 656,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 669,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 668,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 738,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 741,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: selectedStatus === \"\" ? \"default\" : \"outline\",\n                                                                onClick: ()=>setSelectedStatus(\"\"),\n                                                                className: \"h-8\",\n                                                                children: \"全部\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            statusOptions.map((option)=>{\n                                                                const isSelected = selectedStatus === option.value;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: isSelected ? \"default\" : \"outline\",\n                                                                    onClick: ()=>handleStatusSelect(option.value),\n                                                                    className: \"h-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        option.label\n                                                                    ]\n                                                                }, option.value, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: translationStatusOptions.map((option)=>{\n                                                            const isSelected = selectedTranslationStatus === option.value;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: isSelected ? \"default\" : \"outline\",\n                                                                onClick: ()=>handleTranslationStatusSelect(option.value),\n                                                                className: \"h-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 806,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"翻译-\",\n                                                                    option.label\n                                                                ]\n                                                            }, option.value, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_15__.Popover, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_15__.PopoverTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 817,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"操作时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 818,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 820,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 824,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_15__.PopoverContent, {\n                                                                className: \"p-0\",\n                                                                align: \"start\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_14__.Calendar, {\n                                                                    mode: \"range\",\n                                                                    dateRange: dateRange,\n                                                                    onDateRangeChange: setDateRange\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 859,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 906,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 932,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 931,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 952,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 955,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 958,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 968,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 969,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 966,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 976,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1006,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1005,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1010,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1009,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1013,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1016,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1015,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1022,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1021,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1029,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1028,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1033,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1032,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1039,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1038,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1042,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1047,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1043,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1066,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 683,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1109,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1121,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.BatchTranslationProgress, {\n                open: batchTranslationProgress.isOpen,\n                onOpenChange: batchTranslationProgress.setIsOpen,\n                items: batchTranslationProgress.items,\n                isProcessing: batchTranslationProgress.isProcessing,\n                onCancel: batchTranslationProgress.cancelTranslation,\n                onComplete: async ()=>{\n                    // 翻译完成后刷新数据\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    // 延迟关闭对话框，让用户看到完成状态\n                    setTimeout(()=>{\n                        batchTranslationProgress.closeDialog();\n                    }, 2000);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 682,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"GLD4Zw0MhlCtBjB1Gp+hwSXe+nE=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});