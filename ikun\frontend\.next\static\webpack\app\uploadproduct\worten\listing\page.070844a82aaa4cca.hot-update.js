"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态\n    const [selectedStatuses, setSelectedStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTranslationStatuses, setSelectedTranslationStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数\n    const handleStatusToggle = (status)=>{\n        setSelectedStatuses((prev)=>prev.includes(status) ? prev.filter((s)=>s !== status) : [\n                ...prev,\n                status\n            ]);\n    };\n    const handleTranslationStatusToggle = (status)=>{\n        setSelectedTranslationStatuses((prev)=>prev.includes(status) ? prev.filter((s)=>s !== status) : [\n                ...prev,\n                status\n            ]);\n    };\n    const handleReset = ()=>{\n        setSelectedStatuses([]);\n        setSelectedTranslationStatuses([]);\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatuses.length > 0) {\n            params.status = selectedStatuses.join(\",\");\n        }\n        if (selectedTranslationStatuses.length > 0) {\n            params.translation_status = selectedTranslationStatuses.join(\",\");\n        }\n        if (dateRange.start) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end) {\n            params.end_date = dateRange.end;\n        }\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatuses,\n        selectedTranslationStatuses,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 604,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 656,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 655,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 668,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 667,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 717,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"全部\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 768,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"状态\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStatuses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: selectedStatuses.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: statusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuCheckboxItem, {\n                                                                        checked: selectedStatuses.includes(option.value),\n                                                                        onCheckedChange: ()=>handleStatusToggle(option.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                lineNumber: 785,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, option.value, true, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 780,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 796,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"翻译状态\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 797,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedTranslationStatuses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: selectedTranslationStatuses.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 803,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: translationStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuCheckboxItem, {\n                                                                        checked: selectedTranslationStatuses.includes(option.value),\n                                                                        onCheckedChange: ()=>handleTranslationStatusToggle(option.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, option.value, true, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 808,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 824,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 825,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-64 p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 837,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.start,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                start: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 838,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 836,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 846,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.end,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                end: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 847,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 872,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 895,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 898,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 928,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 977,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 991,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 989,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1002,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1015,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1023,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1022,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1028,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1027,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1032,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1031,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1035,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1038,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1037,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1044,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1043,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1051,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1050,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1055,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1054,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1061,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1060,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1064,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1069,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1065,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 885,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1084,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1088,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1087,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1083,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 683,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 682,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1131,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1143,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 681,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"AyWzKMoVGB62XyLBNEOJtbP8lfQ=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});