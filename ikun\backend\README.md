# IKUN ERP Backend

IKUN ERP系统的后端API服务，基于Node.js + Express + TypeScript + MySQL构建。

## 🚀 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- npm 8+

### 安装依赖
```bash
npm install
```

### 环境配置
1. 复制环境变量文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的配置：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=ikun

# JWT配置
JWT_SECRET=your-super-secret-jwt-key

# 服务器配置
PORT=3001
```

### 数据库初始化
```bash
# 执行数据库初始化脚本
mysql -u root -p < ../database/init.sql
```

### 启动开发服务器
```bash
npm run dev
```

服务器将在 http://localhost:3001 启动

## 📁 项目结构

```
backend/
├── src/
│   ├── config/           # 配置文件
│   ├── controllers/      # 控制器层
│   ├── services/         # 业务逻辑层
│   ├── routes/           # 路由定义
│   ├── middleware/       # 中间件
│   ├── utils/            # 工具函数
│   ├── types/            # TypeScript类型
│   ├── database/         # 数据库连接
│   ├── validations/      # 请求验证
│   └── server.ts         # 应用入口
├── tests/                # 测试文件
├── uploads/              # 文件上传目录
├── logs/                 # 日志文件
└── dist/                 # 编译输出
```

## 🛠️ 开发命令

```bash
# 开发模式启动
npm run dev

# 构建项目
npm run build

# 生产模式启动
npm start

# 运行测试
npm test

# 代码检查
npm run lint

# 修复代码格式
npm run lint:fix
```

## 📚 API文档

### 基础信息
- **基础URL**: `http://localhost:3001/api/v1`
- **认证方式**: JWT Bearer Token
- **响应格式**: JSON

### 主要端点
- **认证**: `/api/v1/auth/*`
- **产品目录**: `/api/v1/categories/*`
- **产品管理**: `/api/v1/products/*`
- **店铺管理**: `/api/v1/stores/*`
- **产品上架**: `/api/v1/uploadproduct/*`
- **翻译服务**: `/api/v1/translation/*`
- **采集任务**: `/api/v1/scraping/*` (TODO)
- **订单管理**: `/api/v1/orders/*` (TODO)
- **文件上传**: `/api/v1/upload/*` (TODO)
- **统计分析**: `/api/v1/analytics/*` (TODO)

### API文档访问
访问 http://localhost:3001/api/v1/docs 查看完整API文档

## 🔧 配置说明

### 环境变量
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| NODE_ENV | 运行环境 | development |
| PORT | 服务端口 | 3001 |
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 3306 |
| DB_USER | 数据库用户 | root |
| DB_PASSWORD | 数据库密码 | 123456 |
| DB_NAME | 数据库名称 | ikun |
| JWT_SECRET | JWT密钥 | - |

### 数据库配置
- 使用MySQL 8.0+
- 字符集：utf8mb4
- 时区：+08:00 (中国时区)
- 连接池：10个连接

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行特定测试
npm test -- --grep "CategoryService"

# 生成覆盖率报告
npm run test:coverage
```

### 测试结构
```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
└── fixtures/       # 测试数据
```

## 📊 开发进度

### 已完成 (30%)
- ✅ 基础架构搭建
- ✅ 数据库连接
- ✅ 中间件配置
- ✅ 产品目录管理 (80%)
- ✅ 产品管理框架 (60%)

### 进行中
- 🔄 产品管理服务层
- 🔄 请求验证规则

### 待开发
- ⏳ 认证系统
- ⏳ 文件上传
- ⏳ 采集任务
- ⏳ 订单管理
- ⏳ 统计分析

详细进度请查看 [后端-开发进度文档.md](./后端-开发进度文档.md)

## 📋 开发规范

项目遵循严格的开发规范，包括：
- TypeScript严格模式
- ESLint代码检查
- 分层架构设计
- RESTful API规范
- 统一错误处理
- 完整的类型定义

详细规范请查看 [后端-开发规范.md](./后端-开发规范.md)

## 🔐 安全特性

- JWT认证
- 请求参数验证
- SQL注入防护
- XSS防护
- 请求限流
- CORS配置

## 📝 日志

### 日志级别
- ERROR: 错误信息
- WARN: 警告信息
- INFO: 一般信息
- DEBUG: 调试信息

### 日志文件
- 开发环境：控制台输出
- 生产环境：文件输出到 `logs/` 目录

## 🚀 部署

### 构建生产版本
```bash
npm run build
```

### 启动生产服务
```bash
npm start
```

### Docker部署 (可选)
```bash
# 构建镜像
docker build -t ikun-erp-backend .

# 运行容器
docker run -p 3001:3001 ikun-erp-backend
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 技术支持

- **项目文档**: 查看项目根目录的文档文件
- **API文档**: http://localhost:3001/api/v1/docs
- **开发团队**: IKUN ERP Development Team

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**最后更新**: 2024年1月  
**版本**: 1.0.0  
**维护团队**: IKUN ERP Development Team
