"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态 - 产品状态改为单选\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 单选状态\n    ;\n    const [selectedTranslationStatus, setSelectedTranslationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 翻译状态也改为单选\n    ;\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数 - 改为单选模式\n    const handleStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleTranslationStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedTranslationStatus((prev)=>prev === status ? \"\" : status);\n    };\n    // 日期格式化函数\n    const formatDateDisplay = (dateStr)=>{\n        if (!dateStr) return \"\";\n        // 如果是YYYY-MM-DD格式，转换为YYYY年MM月DD日显示\n        if (dateStr.includes(\"-\")) {\n            const [year, month, day] = dateStr.split(\"-\");\n            if (year && month && day) {\n                return \"\".concat(year, \"年\").concat(month.padStart(2, \"0\"), \"月\").concat(day.padStart(2, \"0\"), \"日\");\n            }\n        }\n        return dateStr;\n    };\n    // 处理日期输入\n    const handleDateInput = (value, type)=>{\n        // 只允许数字输入\n        const numericValue = value.replace(/\\D/g, \"\");\n        if (numericValue.length <= 8) {\n            let formattedValue = numericValue;\n            let displayValue = numericValue;\n            // 自动格式化显示\n            if (numericValue.length >= 4) {\n                const year = numericValue.slice(0, 4);\n                displayValue = \"\".concat(year, \"年\");\n                if (numericValue.length >= 6) {\n                    const month = numericValue.slice(4, 6);\n                    displayValue += \"\".concat(month, \"月\");\n                    if (numericValue.length === 8) {\n                        const day = numericValue.slice(6, 8);\n                        displayValue += \"\".concat(day, \"日\");\n                        // 转换为YYYY-MM-DD格式存储\n                        formattedValue = \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n                    }\n                }\n            }\n            // 如果输入完整的8位数字，转换为标准日期格式\n            if (numericValue.length === 8) {\n                const year = numericValue.slice(0, 4);\n                const month = numericValue.slice(4, 6);\n                const day = numericValue.slice(6, 8);\n                // 验证日期有效性\n                const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\n                if (date.getFullYear() == parseInt(year) && date.getMonth() == parseInt(month) - 1 && date.getDate() == parseInt(day)) {\n                    formattedValue = \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n                }\n            }\n            setDateRange((prev)=>({\n                    ...prev,\n                    [type]: numericValue.length === 8 ? formattedValue : numericValue\n                }));\n        }\n    };\n    const handleReset = ()=>{\n        setSelectedStatus(\"\");\n        setSelectedTranslationStatus(\"\");\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatus) {\n            params.status = selectedStatus;\n        }\n        if (selectedTranslationStatus) {\n            params.translation_status = selectedTranslationStatus;\n        }\n        if (dateRange.start) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end) {\n            params.end_date = dateRange.end;\n        }\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatus,\n        selectedTranslationStatus,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 536,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 549,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 660,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 656,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 709,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 708,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 707,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 720,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 719,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 796,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 800,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 804,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: selectedStatus === \"\" ? \"default\" : \"outline\",\n                                                                onClick: ()=>setSelectedStatus(\"\"),\n                                                                className: \"h-8\",\n                                                                children: \"全部\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            statusOptions.map((option)=>{\n                                                                const isSelected = selectedStatus === option.value;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: isSelected ? \"default\" : \"outline\",\n                                                                    onClick: ()=>handleStatusSelect(option.value),\n                                                                    className: \"h-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 837,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        option.label\n                                                                    ]\n                                                                }, option.value, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: translationStatusOptions.map((option)=>{\n                                                            const isSelected = selectedTranslationStatus === option.value;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: isSelected ? \"default\" : \"outline\",\n                                                                onClick: ()=>handleTranslationStatusSelect(option.value),\n                                                                className: \"h-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 857,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"翻译-\",\n                                                                    option.label\n                                                                ]\n                                                            }, option.value, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 868,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 869,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-64 p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 881,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    placeholder: \"年/月/日\",\n                                                                                    value: formatDateDisplay(dateRange.start),\n                                                                                    onChange: (e)=>handleDateInput(e.target.value, \"start\"),\n                                                                                    className: \"mt-1\",\n                                                                                    maxLength: 10\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 882,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 880,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 891,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    placeholder: \"年/月/日\",\n                                                                                    value: formatDateDisplay(dateRange.end),\n                                                                                    onChange: (e)=>handleDateInput(e.target.value, \"end\"),\n                                                                                    className: \"mt-1\",\n                                                                                    maxLength: 10\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 892,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 890,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 918,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 906,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 935,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 936,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 940,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 948,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 959,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 962,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 966,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 969,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 968,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1000,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1008,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1020,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1019,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1036,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1037,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1035,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1044,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1054,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1061,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1060,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1069,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1068,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1067,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1074,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1073,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1078,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1077,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1081,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1084,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1083,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1090,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1089,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1097,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1096,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1101,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1100,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1107,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1106,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1110,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1115,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1111,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1066,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1065,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 990,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1134,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1150,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 735,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 734,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1177,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1189,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.BatchTranslationProgress, {\n                open: batchTranslationProgress.isOpen,\n                onOpenChange: batchTranslationProgress.setIsOpen,\n                items: batchTranslationProgress.items,\n                isProcessing: batchTranslationProgress.isProcessing,\n                onCancel: batchTranslationProgress.cancelTranslation,\n                onComplete: async ()=>{\n                    // 翻译完成后刷新数据\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    // 延迟关闭对话框，让用户看到完成状态\n                    setTimeout(()=>{\n                        batchTranslationProgress.closeDialog();\n                    }, 2000);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 733,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"dWHnEtktIVXSiFxIRpDIyJSCiHE=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VwbG9hZHByb2R1Y3Qvd29ydGVuL3dvcnRlbi1saXN0aW5nLXBhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ2tCO0FBQ2hCO0FBQ29CO0FBQ047QUFDZjtBQUVzQztBQUNUO0FBQ3lDO0FBQzFEO0FBQ1Q7QUFDRjtBQUNNO0FBT2I7QUFzQmpCO0FBRXJCLE9BQU87QUFDUCxNQUFNeUMsZ0JBQWdCO0lBQ3BCO1FBQUVDLE9BQU87UUFBU0MsT0FBTztRQUFNQyxNQUFNWixxUEFBUUE7SUFBQztJQUM5QztRQUFFVSxPQUFPO1FBQVdDLE9BQU87UUFBT0MsTUFBTU4scVBBQUtBO0lBQUM7SUFDOUM7UUFBRUksT0FBTztRQUFVQyxPQUFPO1FBQU9DLE1BQU1QLHFQQUFXQTtJQUFDO0lBQ25EO1FBQUVLLE9BQU87UUFBVUMsT0FBTztRQUFRQyxNQUFNTCxxUEFBV0E7SUFBQztJQUNwRDtRQUFFRyxPQUFPO1FBQVlDLE9BQU87UUFBT0MsTUFBTWpCLHFQQUFNQTtJQUFDO0NBQ2pEO0FBRUQsU0FBUztBQUNULE1BQU1rQiwyQkFBMkI7SUFDL0I7UUFBRUgsT0FBTztRQUFXQyxPQUFPO1FBQU9DLE1BQU1OLHFQQUFLQTtJQUFDO0lBQzlDO1FBQUVJLE9BQU87UUFBYUMsT0FBTztRQUFPQyxNQUFNUCxxUEFBV0E7SUFBQztDQUN2RDtBQU1NLFNBQVNTLHNCQUFzQixLQUF3QztRQUF4QyxFQUFFQyxRQUFRLEVBQThCLEdBQXhDO1FBMG1DdEJDOztJQXptQ2QsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHbEQsK0NBQVFBLENBQVcsRUFBRTtJQUNyRSxNQUFNLENBQUNtRCxhQUFhQyxlQUFlLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNxRCxlQUFlQyxpQkFBaUIsR0FBR3RELCtDQUFRQSxDQUFTO0lBQzNELE1BQU0sQ0FBQ3VELFVBQVVDLFlBQVksR0FBR3hELCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3lELGdCQUFnQkMsa0JBQWtCLEdBQUcxRCwrQ0FBUUEsQ0FBOEI7SUFDbEYsTUFBTSxFQUFFMkQsT0FBTyxFQUFFLEdBQUd0RCx5RUFBVUE7SUFDOUIsTUFBTSxFQUFFdUQsS0FBSyxFQUFFLEdBQUd0RCwwREFBUUE7SUFFMUIsa0JBQWtCO0lBQ2xCLE1BQU0sQ0FBQ3VELGdCQUFnQkMsa0JBQWtCLEdBQUc5RCwrQ0FBUUEsQ0FBUyxJQUFJLE9BQU87O0lBQ3hFLE1BQU0sQ0FBQytELDJCQUEyQkMsNkJBQTZCLEdBQUdoRSwrQ0FBUUEsQ0FBUyxJQUFJLFlBQVk7O0lBQ25HLE1BQU0sQ0FBQ2lFLFdBQVdDLGFBQWEsR0FBR2xFLCtDQUFRQSxDQUErQjtRQUN2RW1FLE9BQU87UUFDUEMsS0FBSztJQUNQO0lBRUEsVUFBVTtJQUNWLE1BQU0sQ0FBQ0Msc0JBQXNCQyx3QkFBd0IsR0FBR3RFLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ2dELG9CQUFvQnVCLHNCQUFzQixHQUFHdkUsK0NBQVFBLENBQThCO0lBQzFGLE1BQU0sQ0FBQ3dFLHdCQUF3QkMsMEJBQTBCLEdBQUd6RSwrQ0FBUUEsQ0FBMEI7SUFDOUYsTUFBTSxDQUFDMEUsWUFBWUMsY0FBYyxHQUFHM0UsK0NBQVFBLENBQUMsR0FBRyxVQUFVOztJQUUxRCxXQUFXO0lBQ1gsTUFBTTRFLDJCQUEyQmxFLHNHQUEyQkE7SUFFNUQsY0FBYztJQUNkLE1BQU0sRUFDSm1FLGNBQWMsRUFDZEMsU0FBU0MsZUFBZSxFQUN4QkMsT0FBT0MsYUFBYSxFQUNwQkMsVUFBVSxFQUNWQyxtQkFBbUIsRUFDbkJDLG1CQUFtQixFQUNuQkMsbUJBQW1CLEVBQ25CQyxtQkFBbUIsRUFDcEIsR0FBR3BGLDJFQUFpQkEsQ0FBQzZDO0lBRXRCLE1BQU0sRUFDSndDLE1BQU0sRUFDTlQsU0FBU1UsYUFBYSxFQUN0QkMsV0FBVyxFQUNaLEdBQUd0RiwyREFBU0E7SUFFYixNQUFNLEVBQ0p1RixnQkFBZ0IsRUFDaEJaLFNBQVNhLGVBQWUsRUFDeEJDLHFCQUFxQixFQUN0QixHQUFHeEYsK0VBQW1CQTtJQUV2QixZQUFZO0lBQ1osTUFBTXlGLGlCQUFpQk4sT0FBT08sTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxNQUFNQyxhQUFhLEtBQUtqRDtJQUV0RSxrQkFBa0I7SUFDbEIsTUFBTWtELHFCQUFxQixDQUFDQztRQUMxQiwrQkFBK0I7UUFDL0JwQyxrQkFBa0JxQyxDQUFBQSxPQUFRQSxTQUFTRCxTQUFTLEtBQUtBO0lBQ25EO0lBRUEsTUFBTUUsZ0NBQWdDLENBQUNGO1FBQ3JDLCtCQUErQjtRQUMvQmxDLDZCQUE2Qm1DLENBQUFBLE9BQVFBLFNBQVNELFNBQVMsS0FBS0E7SUFDOUQ7SUFFQSxVQUFVO0lBQ1YsTUFBTUcsb0JBQW9CLENBQUNDO1FBQ3pCLElBQUksQ0FBQ0EsU0FBUyxPQUFPO1FBQ3JCLG1DQUFtQztRQUNuQyxJQUFJQSxRQUFRQyxRQUFRLENBQUMsTUFBTTtZQUN6QixNQUFNLENBQUNDLE1BQU1DLE9BQU9DLElBQUksR0FBR0osUUFBUUssS0FBSyxDQUFDO1lBQ3pDLElBQUlILFFBQVFDLFNBQVNDLEtBQUs7Z0JBQ3hCLE9BQU8sR0FBV0QsT0FBUkQsTUFBSyxLQUE2QkUsT0FBMUJELE1BQU1HLFFBQVEsQ0FBQyxHQUFHLE1BQUssS0FBd0IsT0FBckJGLElBQUlFLFFBQVEsQ0FBQyxHQUFHLE1BQUs7WUFDbkU7UUFDRjtRQUNBLE9BQU9OO0lBQ1Q7SUFFQSxTQUFTO0lBQ1QsTUFBTU8sa0JBQWtCLENBQUNuRSxPQUFlb0U7UUFDdEMsVUFBVTtRQUNWLE1BQU1DLGVBQWVyRSxNQUFNc0UsT0FBTyxDQUFDLE9BQU87UUFFMUMsSUFBSUQsYUFBYUUsTUFBTSxJQUFJLEdBQUc7WUFDNUIsSUFBSUMsaUJBQWlCSDtZQUNyQixJQUFJSSxlQUFlSjtZQUVuQixVQUFVO1lBQ1YsSUFBSUEsYUFBYUUsTUFBTSxJQUFJLEdBQUc7Z0JBQzVCLE1BQU1ULE9BQU9PLGFBQWFLLEtBQUssQ0FBQyxHQUFHO2dCQUNuQ0QsZUFBZSxHQUFRLE9BQUxYLE1BQUs7Z0JBRXZCLElBQUlPLGFBQWFFLE1BQU0sSUFBSSxHQUFHO29CQUM1QixNQUFNUixRQUFRTSxhQUFhSyxLQUFLLENBQUMsR0FBRztvQkFDcENELGdCQUFnQixHQUFTLE9BQU5WLE9BQU07b0JBRXpCLElBQUlNLGFBQWFFLE1BQU0sS0FBSyxHQUFHO3dCQUM3QixNQUFNUCxNQUFNSyxhQUFhSyxLQUFLLENBQUMsR0FBRzt3QkFDbENELGdCQUFnQixHQUFPLE9BQUpULEtBQUk7d0JBQ3ZCLG9CQUFvQjt3QkFDcEJRLGlCQUFpQixHQUFXVCxPQUFSRCxNQUFLLEtBQVlFLE9BQVRELE9BQU0sS0FBTyxPQUFKQztvQkFDdkM7Z0JBQ0Y7WUFDRjtZQUVBLHdCQUF3QjtZQUN4QixJQUFJSyxhQUFhRSxNQUFNLEtBQUssR0FBRztnQkFDN0IsTUFBTVQsT0FBT08sYUFBYUssS0FBSyxDQUFDLEdBQUc7Z0JBQ25DLE1BQU1YLFFBQVFNLGFBQWFLLEtBQUssQ0FBQyxHQUFHO2dCQUNwQyxNQUFNVixNQUFNSyxhQUFhSyxLQUFLLENBQUMsR0FBRztnQkFFbEMsVUFBVTtnQkFDVixNQUFNQyxPQUFPLElBQUlDLEtBQUtDLFNBQVNmLE9BQU9lLFNBQVNkLFNBQVMsR0FBR2MsU0FBU2I7Z0JBQ3BFLElBQUlXLEtBQUtHLFdBQVcsTUFBTUQsU0FBU2YsU0FDL0JhLEtBQUtJLFFBQVEsTUFBTUYsU0FBU2QsU0FBUyxLQUNyQ1ksS0FBS0ssT0FBTyxNQUFNSCxTQUFTYixNQUFNO29CQUNuQ1EsaUJBQWlCLEdBQVdULE9BQVJELE1BQUssS0FBWUUsT0FBVEQsT0FBTSxLQUFPLE9BQUpDO2dCQUN2QztZQUNGO1lBRUF4QyxhQUFhaUMsQ0FBQUEsT0FBUztvQkFDcEIsR0FBR0EsSUFBSTtvQkFDUCxDQUFDVyxLQUFLLEVBQUVDLGFBQWFFLE1BQU0sS0FBSyxJQUFJQyxpQkFBaUJIO2dCQUN2RDtRQUNGO0lBQ0Y7SUFFQSxNQUFNWSxjQUFjO1FBQ2xCN0Qsa0JBQWtCO1FBQ2xCRSw2QkFBNkI7UUFDN0JFLGFBQWE7WUFBRUMsT0FBTztZQUFJQyxLQUFLO1FBQUc7UUFDbENoQixlQUFlO1FBQ2ZFLGlCQUFpQjtRQUNqQixXQUFXO1FBQ1g2QjtJQUNGO0lBRUEsT0FBTztJQUNQLE1BQU15QyxxQkFBcUI7UUFDekIsTUFBTUMsU0FBYyxDQUFDO1FBRXJCLElBQUkxRSxhQUFhO1lBQ2YwRSxPQUFPQyxNQUFNLEdBQUczRTtRQUNsQjtRQUVBLElBQUlFLGlCQUFpQkEsa0JBQWtCLE9BQU87WUFDNUN3RSxPQUFPRSxRQUFRLEdBQUcxRTtRQUNwQjtRQUVBLElBQUlRLGdCQUFnQjtZQUNsQmdFLE9BQU8zQixNQUFNLEdBQUdyQztRQUNsQjtRQUVBLElBQUlFLDJCQUEyQjtZQUM3QjhELE9BQU9HLGtCQUFrQixHQUFHakU7UUFDOUI7UUFFQSxJQUFJRSxVQUFVRSxLQUFLLEVBQUU7WUFDbkIwRCxPQUFPSSxVQUFVLEdBQUdoRSxVQUFVRSxLQUFLO1FBQ3JDO1FBRUEsSUFBSUYsVUFBVUcsR0FBRyxFQUFFO1lBQ2pCeUQsT0FBT0ssUUFBUSxHQUFHakUsVUFBVUcsR0FBRztRQUNqQztRQUVBZSxvQkFBb0IwQztJQUN0QjtJQUVBLGtCQUFrQjtJQUNsQjVILGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWtJLFFBQVFDLFdBQVc7WUFDdkJSO1FBQ0YsR0FBRyxLQUFLLE9BQU87O1FBRWYsT0FBTyxJQUFNUyxhQUFhRjtJQUM1QixHQUFHO1FBQUNoRjtRQUFhRTtRQUFlUTtRQUFnQkU7UUFBMkJFO0tBQVU7SUFFckYsU0FBUztJQUNULE1BQU1xRSxzQkFBc0IsT0FBT0M7UUFDakMsTUFBTW5ELG9CQUFvQm1EO1FBQzFCL0UsWUFBWTtJQUNkO0lBRUEsV0FBVztJQUNYLE1BQU1nRixzQkFBc0IsT0FBT0Q7UUFDakMsSUFBSSxDQUFDOUUsZ0JBQWdCO1lBQ25CRyxNQUFNO2dCQUNKNkUsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsU0FBUztZQUNYO1lBQ0E7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNdEQsb0JBQW9CNUIsZUFBZW1GLEVBQUUsRUFBRUw7WUFDN0MzRSxNQUFNO2dCQUNKNkUsT0FBTztnQkFDUEMsYUFBYTtZQUNmO1lBQ0EsU0FBUztZQUNULE1BQU12RDtZQUNOM0IsWUFBWTtZQUNaRSxrQkFBa0I7UUFDcEIsRUFBRSxPQUFPc0IsT0FBTztZQUNkNkQsUUFBUTdELEtBQUssQ0FBQyxXQUFXQTtZQUN6QnBCLE1BQU07Z0JBQ0o2RSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7WUFDQSxNQUFNM0Q7UUFDUjtJQUNGO0lBRUEsYUFBYTtJQUNiLE1BQU04RCxtQkFBbUJyRixpQkFBaUIrRSxzQkFBc0JGO0lBRWhFLE1BQU1TLG9CQUFvQixDQUFDQztRQUN6QnRGLGtCQUFrQnNGO1FBQ2xCeEYsWUFBWTtJQUNkO0lBRUEsTUFBTXlGLGtCQUFrQjtRQUN0QnpGLFlBQVk7UUFDWkUsa0JBQWtCO0lBQ3BCO0lBRUEsUUFBUTtJQUNSekQsZ0RBQVNBLENBQUM7UUFDUndGO1FBQ0FOO1FBQ0FTO0lBQ0YsR0FBRztRQUFDN0M7S0FBUztJQUViLE1BQU1tRyxzQkFBc0IsT0FBT0M7UUFDakMsTUFBTUMsWUFBWSxNQUFNekYsUUFBUTtZQUM5QjhFLE9BQU87WUFDUEMsYUFBYTtZQUNiVyxhQUFhO1lBQ2JDLFlBQVk7WUFDWlgsU0FBUztRQUNYO1FBRUEsSUFBSSxDQUFDUyxXQUFXO1lBQ2Q7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNOUQsb0JBQW9CNkQ7WUFDMUJqRyxvQkFBb0JpRCxDQUFBQSxPQUFRQSxLQUFLTCxNQUFNLENBQUM4QyxDQUFBQSxLQUFNQSxPQUFPTztZQUNyRHZGLE1BQU07Z0JBQ0o4RSxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRixFQUFFLE9BQU8zRCxPQUFPO1lBQ2Q2RCxRQUFRN0QsS0FBSyxDQUFDLFdBQVdBO1lBQ3pCcEIsTUFBTTtnQkFDSjhFLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNWSxvQkFBb0I7UUFDeEIsSUFBSXRHLGlCQUFpQmdFLE1BQU0sS0FBSyxHQUFHO1lBQ2pDLE1BQU10RCxRQUFRO2dCQUNaOEUsT0FBTztnQkFDUEMsYUFBYTtnQkFDYlcsYUFBYTtnQkFDYlYsU0FBUztnQkFDVC9GLE1BQU07WUFDUjtZQUNBO1FBQ0Y7UUFFQSxNQUFNd0csWUFBWSxNQUFNekYsUUFBUTtZQUM5QjhFLE9BQU87WUFDUEMsYUFBYSxZQUFvQyxPQUF4QnpGLGlCQUFpQmdFLE1BQU0sRUFBQztZQUNqRG9DLGFBQWEsTUFBOEIsT0FBeEJwRyxpQkFBaUJnRSxNQUFNLEVBQUM7WUFDM0NxQyxZQUFZO1lBQ1pYLFNBQVM7UUFDWDtRQUVBLElBQUksQ0FBQ1MsV0FBVztZQUNkO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsU0FBUztZQUNULE1BQU1JLFFBQVFDLEdBQUcsQ0FBQ3hHLGlCQUFpQnlHLEdBQUcsQ0FBQ2QsQ0FBQUEsS0FBTXRELG9CQUFvQnNEO1lBQ2pFMUYsb0JBQW9CLEVBQUU7WUFDdEJVLE1BQU07Z0JBQ0o4RSxhQUFhLFNBQWlDLE9BQXhCekYsaUJBQWlCZ0UsTUFBTSxFQUFDO2dCQUM5QzBCLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBTzNELE9BQU87WUFDZDZELFFBQVE3RCxLQUFLLENBQUMsV0FBV0E7WUFDekJwQixNQUFNO2dCQUNKOEUsYUFBYTtnQkFDYkMsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU1nQix5QkFBeUI7UUFDN0IsSUFBSTFHLGlCQUFpQmdFLE1BQU0sS0FBSyxHQUFHO1lBQ2pDLE1BQU10RCxRQUFRO2dCQUNaOEUsT0FBTztnQkFDUEMsYUFBYTtnQkFDYlcsYUFBYTtnQkFDYlYsU0FBUztnQkFDVC9GLE1BQU07WUFDUjtZQUNBO1FBQ0Y7UUFFQSxNQUFNd0csWUFBWSxNQUFNekYsUUFBUTtZQUM5QjhFLE9BQU87WUFDUEMsYUFBYSxZQUFvQyxPQUF4QnpGLGlCQUFpQmdFLE1BQU0sRUFBQztZQUNqRG9DLGFBQWEsTUFBOEIsT0FBeEJwRyxpQkFBaUJnRSxNQUFNLEVBQUM7WUFDM0NxQyxZQUFZO1lBQ1pYLFNBQVM7UUFDWDtRQUVBLElBQUksQ0FBQ1MsV0FBVztZQUNkO1FBQ0Y7UUFFQSxTQUFTO1FBQ1QsTUFBTVEsdUJBQXVCL0UsZUFBZWlCLE1BQU0sQ0FBQytELENBQUFBLElBQUs1RyxpQkFBaUJzRCxRQUFRLENBQUNzRCxFQUFFakIsRUFBRTtRQUN0RixNQUFNa0IsZ0JBQWdCRixxQkFBcUJGLEdBQUcsQ0FBQ1YsQ0FBQUEsVUFBWTtnQkFDekRKLElBQUlJLFFBQVFKLEVBQUU7Z0JBQ2RtQixLQUFLZixRQUFRZSxHQUFHO2dCQUNoQkMsTUFBTWhCLFFBQVFpQixhQUFhLElBQUksTUFBaUIsT0FBWGpCLFFBQVFKLEVBQUU7WUFDakQ7UUFFQSxVQUFVO1FBQ1ZoRSx5QkFBeUJzRixnQkFBZ0IsQ0FBQ0o7UUFFMUMsSUFBSTtnQkFxQ3lCSztZQXBDM0IsYUFBYTtZQUNiLE1BQU1DLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxJQUFJLENBQUNGLE9BQU87Z0JBQ1YsTUFBTSxJQUFJRyxNQUFNO1lBQ2xCO1lBRUEsMEJBQTBCO1lBQzFCdEgsaUJBQWlCdUgsT0FBTyxDQUFDLENBQUNyQixXQUFXc0I7Z0JBQ25DckMsV0FBVztvQkFDVHhELHlCQUF5QjhGLGlCQUFpQixDQUFDdkI7Z0JBQzdDLEdBQUdzQixRQUFRLEtBQUssbUJBQW1COztZQUNyQztZQUVBLGFBQWE7WUFDYixNQUFNRSxXQUFXLE1BQU1DLE1BQU0sc0NBQXNDO2dCQUNqRUMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQixVQUFnQixPQUFOVjtnQkFDN0I7Z0JBQ0FXLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJsSSxVQUFVO29CQUNWbUksUUFBUTtvQkFDUkMsWUFBWTtvQkFDWkMsYUFBYUMsMkJBQTJCO29CQUN4Q0MsWUFBWXJJLGlCQUFpQnNJLElBQUksQ0FBQztnQkFDcEM7WUFDRjtZQUVBLElBQUksQ0FBQ1osU0FBU2EsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlqQixNQUFNO1lBQ2xCO1lBRUEsTUFBTUosU0FBUyxNQUFNUSxTQUFTYyxJQUFJO1lBRWxDLCtCQUErQjtZQUMvQixJQUFJdEIsT0FBT3VCLElBQUksS0FBSyxTQUFPdkIsZUFBQUEsT0FBTzVCLElBQUksY0FBWDRCLG1DQUFBQSxhQUFhd0IsT0FBTyxHQUFFO2dCQUMvQyxTQUFTO2dCQUNULE1BQU0sRUFBRUMsT0FBTyxFQUFFLEdBQUd6QixPQUFPNUIsSUFBSTtnQkFDL0IsTUFBTXNELGVBQWVELENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU0UsVUFBVSxLQUFJO2dCQUM1QyxNQUFNQyxjQUFjSCxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNJLE1BQU0sS0FBSTtnQkFDdkMsTUFBTUMsVUFBVUwsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTSyxPQUFPLEtBQUksRUFBRTtnQkFFdEMsY0FBYztnQkFDZEEsUUFBUXpCLE9BQU8sQ0FBQyxDQUFDMEI7b0JBQ2YsSUFBSUEsT0FBT1AsT0FBTyxFQUFFO3dCQUNsQi9HLHlCQUF5QnVILGNBQWMsQ0FBQ0QsT0FBTy9DLFNBQVMsRUFBRTtvQkFDNUQsT0FBTzt3QkFDTHZFLHlCQUF5QndILFlBQVksQ0FBQ0YsT0FBTy9DLFNBQVMsRUFBRStDLE9BQU9sSCxLQUFLLElBQUk7b0JBQzFFO2dCQUNGO2dCQUVBLFNBQVM7Z0JBQ1RKLHlCQUF5QnlILGlCQUFpQjtnQkFFMUNuSixvQkFBb0IsRUFBRTtnQkFFdEIsSUFBSTJJLGVBQWUsR0FBRztvQkFDcEJqSSxNQUFNO3dCQUNKNkUsT0FBTzt3QkFDUEMsYUFBYSxRQUEyQnFELE9BQW5CRixjQUFhLFFBQW9ELE9BQTlDRSxjQUFjLElBQUksT0FBbUIsT0FBWkEsYUFBWSxRQUFNO3dCQUNuRnBELFNBQVM7b0JBQ1g7Z0JBQ0YsT0FBTztvQkFDTC9FLE1BQU07d0JBQ0o2RSxPQUFPO3dCQUNQQyxhQUFjO3dCQUNkQyxTQUFTO29CQUNYO2dCQUNGO2dCQUVBLHVCQUF1QjtnQkFDdkJFLFFBQVF5RCxHQUFHLENBQUM7Z0JBRVosY0FBYztnQkFDZCxNQUFNbkgsb0JBQW9CO29CQUN4Qm9ILElBQUlqRixLQUFLa0YsR0FBRztvQkFDWkMsZUFBZTtnQkFDakI7Z0JBRUE1RCxRQUFReUQsR0FBRyxDQUFDLHlCQUF5QnpILGVBQWV1QyxLQUFLLENBQUMsR0FBRyxHQUFHc0MsR0FBRyxDQUFDRyxDQUFBQSxJQUFNO3dCQUN4RWpCLElBQUlpQixFQUFFakIsRUFBRTt3QkFDUm1CLEtBQUtGLEVBQUVFLEdBQUc7d0JBQ1YyQyxjQUFjN0MsRUFBRTZDLFlBQVk7d0JBQzVCQyxvQkFBb0I5QyxFQUFFOEMsa0JBQWtCO29CQUMxQztnQkFFQTlELFFBQVF5RCxHQUFHLENBQUMsb0JBQW9CNUg7Z0JBRWhDLFdBQVc7Z0JBQ1hDLGNBQWN3QixDQUFBQSxPQUFRQSxPQUFPO2dCQUU3QixnQkFBZ0I7Z0JBQ2hCaUMsV0FBVztvQkFDVFMsUUFBUXlELEdBQUcsQ0FBQztvQkFDWixNQUFNbkgsb0JBQW9CO3dCQUFFb0gsSUFBSWpGLEtBQUtrRixHQUFHO29CQUFHO29CQUMzQzdILGNBQWN3QixDQUFBQSxPQUFRQSxPQUFPO29CQUM3QjBDLFFBQVF5RCxHQUFHLENBQUM7Z0JBQ2QsR0FBRztZQUNMLE9BQU87b0JBQ1duQztnQkFBaEIsTUFBTSxJQUFJSSxNQUFNSixFQUFBQSxnQkFBQUEsT0FBTzVCLElBQUksY0FBWDRCLG9DQUFBQSxjQUFheUMsT0FBTyxLQUFJekMsT0FBT3lDLE9BQU8sSUFBSTtZQUM1RDtRQUNGLEVBQUUsT0FBTzVILE9BQU87WUFDZDZELFFBQVE3RCxLQUFLLENBQUMsV0FBV0E7WUFFekIsWUFBWTtZQUNaL0IsaUJBQWlCdUgsT0FBTyxDQUFDckIsQ0FBQUE7Z0JBQ3ZCdkUseUJBQXlCd0gsWUFBWSxDQUFDakQsV0FBV25FLGlCQUFpQnVGLFFBQVF2RixNQUFNNEgsT0FBTyxHQUFHO1lBQzVGO1lBRUEsU0FBUztZQUNUaEkseUJBQXlCeUgsaUJBQWlCO1lBRTFDekksTUFBTTtnQkFDSjhFLGFBQWExRCxpQkFBaUJ1RixRQUFRdkYsTUFBTTRILE9BQU8sR0FBRztnQkFDdERqRSxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTWtFLGlCQUFpQixDQUFDM0c7UUFDdEIsTUFBTTRHLFlBQVk7WUFDaEJDLE9BQU87Z0JBQUVwSyxPQUFPO2dCQUFNcUssV0FBVztZQUE0QjtZQUM3REMsU0FBUztnQkFBRXRLLE9BQU87Z0JBQU9xSyxXQUFXO1lBQWdDO1lBQ3BFRSxRQUFRO2dCQUFFdkssT0FBTztnQkFBT3FLLFdBQVc7WUFBOEI7WUFDakVoQixRQUFRO2dCQUFFckosT0FBTztnQkFBUXFLLFdBQVc7WUFBMEI7WUFDOURHLFVBQVU7Z0JBQUV4SyxPQUFPO2dCQUFPcUssV0FBVztZQUE0QjtRQUNuRTtRQUNBLE1BQU1JLFNBQVNOLFNBQVMsQ0FBQzVHLE9BQWlDLElBQUk0RyxVQUFVQyxLQUFLO1FBQzdFLHFCQUNFLDhEQUFDTTtZQUFLTCxXQUFXLHNFQUF1RixPQUFqQkksT0FBT0osU0FBUztzQkFDcEdJLE9BQU96SyxLQUFLOzs7Ozs7SUFHbkI7SUFFQSxNQUFNMkssNEJBQTRCLENBQUNwSDtRQUNqQyxNQUFNNEcsWUFBWTtZQUNoQkcsU0FBUztnQkFBRXRLLE9BQU87Z0JBQU9xSyxXQUFXO1lBQWdDO1lBQ3BFTyxXQUFXO2dCQUFFNUssT0FBTztnQkFBT3FLLFdBQVc7WUFBOEI7UUFDdEU7UUFDQSxNQUFNSSxTQUFTTixTQUFTLENBQUM1RyxPQUFpQyxJQUFJNEcsVUFBVUcsT0FBTztRQUMvRSxxQkFDRSw4REFBQ0k7WUFBS0wsV0FBVyx1RUFBd0YsT0FBakJJLE9BQU9KLFNBQVM7c0JBQ3JHSSxPQUFPekssS0FBSzs7Ozs7O0lBR25CO0lBRUEsV0FBVztJQUNYLE1BQU02SyxrQkFBa0IsQ0FBQ0M7UUFDdkIsTUFBTUMsY0FBc0M7WUFDMUNDLFFBQVE7WUFDUkMsS0FBSztZQUNMQyxRQUFRO1lBQ1JDLE1BQU07WUFDTkMsU0FBUztRQUNYO1FBQ0EsT0FBT0wsV0FBVyxDQUFDRCxhQUFhLElBQUlBLGFBQWFPLFdBQVc7SUFDOUQ7SUFFQSxjQUFjO0lBQ2QsTUFBTUMsK0JBQStCLENBQUNSO1FBQ3BDLE1BQU1TLG9CQUE4QztZQUNsRFAsUUFBUTtnQkFBQztnQkFBTTthQUFLO1lBQ3BCQyxLQUFLO2dCQUFDO2dCQUFNO2dCQUFNO2dCQUFNO2FBQUs7WUFDN0JDLFFBQVE7Z0JBQUM7Z0JBQU07Z0JBQU07Z0JBQU07Z0JBQU07YUFBSztZQUN0Q0MsTUFBTTtnQkFBQzthQUFLO1lBQ1pDLFNBQVM7Z0JBQUM7YUFBSztRQUNqQjtRQUNBLE9BQU9HLGlCQUFpQixDQUFDVCxhQUFhLElBQUk7WUFBQztTQUFLO0lBQ2xEO0lBRUEsZ0NBQWdDO0lBQ2hDLE1BQU1wQyw2QkFBNkIsQ0FBQ29DO1FBQ2xDLE1BQU1VLGNBQTRDO1lBQ2hELE1BQU07WUFDTixNQUFNO1lBQ04sTUFBTTtZQUNOLE1BQU07WUFDTixNQUFNO1lBQ04sTUFBTTtZQUNOLE1BQU07WUFDTixNQUFNO1lBQ04sTUFBTTtZQUNOLE1BQU0sS0FBTSx3QkFBd0I7UUFDdEM7UUFFQSxNQUFNRCxvQkFBb0JELDZCQUE2QlI7UUFDdkQsT0FBT1Msa0JBQWtCeEUsR0FBRyxDQUFDMEUsQ0FBQUEsT0FBUUQsV0FBVyxDQUFDQyxLQUFLLElBQUk7SUFDNUQ7SUFFQSxXQUFXO0lBQ1gsTUFBTUMsMkJBQTJCLENBQUNyRixTQUErQnNGO1FBQy9EL0osc0JBQXNCeUU7UUFDdEJ2RSwwQkFBMEI2SjtRQUMxQmhLLHdCQUF3QjtJQUMxQjtJQUVBLHFCQUFxQjtJQUVyQixTQUFTO0lBQ1QsTUFBTWlLLDRCQUE0QixPQUFPQztRQUN2QyxJQUFJLENBQUN4TCxvQkFBb0I7UUFFekIsSUFBSTtZQUNGLFNBQVM7WUFDVCxNQUFNeUwsYUFBa0IsQ0FBQztZQUV6QixJQUFJakssMkJBQTJCLFNBQVM7Z0JBQ3RDaUssV0FBVy9CLFlBQVksR0FBRztvQkFDeEIsR0FBSTFKLG1CQUFtQjBKLFlBQVksSUFBSSxDQUFDLENBQUM7b0JBQ3pDLEdBQUc4QixZQUFZO2dCQUNqQjtZQUNGLE9BQU87Z0JBQ0xDLFdBQVc5QixrQkFBa0IsR0FBRztvQkFDOUIsR0FBSTNKLG1CQUFtQjJKLGtCQUFrQixJQUFJLENBQUMsQ0FBQztvQkFDL0MsR0FBRzZCLFlBQVk7Z0JBQ2pCO1lBQ0Y7WUFFQSxTQUFTO1lBQ1RDLFdBQVdDLDJCQUEyQixHQUFHO1lBRXpDLFVBQVU7WUFDVixNQUFNckosb0JBQW9CckMsbUJBQW1CNEYsRUFBRSxFQUFFNkY7WUFFakQ3SyxNQUFNO2dCQUNKNkUsT0FBTztnQkFDUEMsYUFBYSxHQUFvRCxPQUFqRGxFLDJCQUEyQixVQUFVLE9BQU8sTUFBSztZQUNuRTtZQUVBLE9BQU87WUFDUCxNQUFNVztRQUVSLEVBQUUsT0FBT0gsT0FBTztZQUNkNkQsUUFBUTdELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDcEIsTUFBTTtnQkFDSjZFLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTWdHLDRCQUE0QixDQUFDQyxlQUE4Q0M7UUFDL0UsTUFBTXRHLE9BQU9xRyxpQkFBaUIsQ0FBQztRQUUvQixxQkFDRSw4REFBQ0U7WUFBSTlCLFdBQVU7c0JBQ1o2QixrQkFBa0JuRixHQUFHLENBQUMwRSxDQUFBQTtnQkFDckIsTUFBTVcsaUJBQWlCeEcsSUFBSSxDQUFDNkYsS0FBSyxJQUFJN0YsSUFBSSxDQUFDNkYsS0FBSyxDQUFDWSxJQUFJLE9BQU87Z0JBQzNELHFCQUNFLDhEQUFDRjtvQkFFQzlCLFdBQVcsMkRBSVYsT0FIQytCLGlCQUNJLGdDQUNBOzhCQUdMWDttQkFQSUE7Ozs7O1lBVVg7Ozs7OztJQUdOO0lBRUEsTUFBTWEsa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUlBLFNBQVM7WUFDWGhNLG9CQUFvQjJCLGVBQWU2RSxHQUFHLENBQUMsQ0FBQ0csSUFBNEJBLEVBQUVqQixFQUFFO1FBQzFFLE9BQU87WUFDTDFGLG9CQUFvQixFQUFFO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFNaU0sc0JBQXNCLENBQUNoRyxXQUFtQitGO1FBQzlDLElBQUlBLFNBQVM7WUFDWGhNLG9CQUFvQjttQkFBSUQ7Z0JBQWtCa0c7YUFBVTtRQUN0RCxPQUFPO1lBQ0xqRyxvQkFBb0JELGlCQUFpQjZDLE1BQU0sQ0FBQzhDLENBQUFBLEtBQU1BLE9BQU9PO1FBQzNEO0lBQ0Y7SUFFQSxZQUFZO0lBQ1osTUFBTWlHLHFCQUFxQixDQUFDakcsV0FBbUJrRztRQUM3QyxlQUFlO1FBQ2YsSUFBSSxNQUFPQyxNQUFNLENBQWlCQyxPQUFPLENBQUMsNkJBQ3RDLE1BQU9ELE1BQU0sQ0FBaUJDLE9BQU8sQ0FBQyxXQUFXO1lBQ25EO1FBQ0Y7UUFFQSxNQUFNQyxhQUFhdk0saUJBQWlCc0QsUUFBUSxDQUFDNEM7UUFDN0NnRyxvQkFBb0JoRyxXQUFXLENBQUNxRztJQUNsQztJQUVBLFNBQVM7SUFDVCxJQUFJekssbUJBQW1CRixlQUFlb0MsTUFBTSxLQUFLLEdBQUc7UUFDbEQscUJBQ0UsOERBQUM2SDtZQUFJOUIsV0FBVTtzQkFDYiw0RUFBQzhCO2dCQUFJOUIsV0FBVTs7a0NBQ2IsOERBQUM4Qjt3QkFBSTlCLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ25EO3dCQUFFbUQsV0FBVTtrQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSTdDO0lBRUEsU0FBUztJQUNULElBQUkvSCxlQUFlO1FBQ2pCLHFCQUNFLDhEQUFDNko7WUFBSTlCLFdBQVU7c0JBQ2IsNEVBQUM4QjtnQkFBSTlCLFdBQVU7O2tDQUNiLDhEQUFDbkQ7d0JBQUVtRCxXQUFVOzs0QkFBb0I7NEJBQU8vSDs7Ozs7OztrQ0FDeEMsOERBQUNwRSwwREFBTUE7d0JBQUM0TyxTQUFTLElBQU10Szt3QkFBdUJ3RCxTQUFRO2tDQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztJQU14RTtJQUVBLE1BQU0rRyxlQUFlbEMsZ0JBQWdCeks7SUFFckMscUJBQ0UsOERBQUMrTDtRQUFJOUIsV0FBVTs7MEJBQ2IsOERBQUNyTSxzREFBSUE7Z0JBQUNxTSxXQUFVOzBCQUNkLDRFQUFDcE0sNkRBQVdBO29CQUFDb00sV0FBVTs7c0NBRXJCLDhEQUFDOEI7NEJBQUk5QixXQUFVOzs4Q0FFYiw4REFBQzhCO29DQUFJOUIsV0FBVTs7c0RBQ2IsOERBQUNuTSwwREFBTUE7NENBQUM4TyxNQUFLOzRDQUFLaEgsU0FBUTs0Q0FBVThHLFNBQVMsSUFBTWpNLFlBQVk7OzhEQUM3RCw4REFBQ25DLHFQQUFJQTtvREFBQzJMLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBR25DLDhEQUFDbk0sMERBQU1BOzRDQUFDOE8sTUFBSzs0Q0FBS2hILFNBQVE7OzhEQUN4Qiw4REFBQ3JILHFQQUFNQTtvREFBQzBMLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBSXJDLDhEQUFDbk0sMERBQU1BOzRDQUFDOE8sTUFBSzs0Q0FBS2hILFNBQVE7OzhEQUN4Qiw4REFBQ2xILHFQQUFTQTtvREFBQ3VMLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBS3hDLDhEQUFDaE0sdUVBQVlBOzs4REFDWCw4REFBQ0csOEVBQW1CQTtvREFBQ3lPLE9BQU87OERBQzFCLDRFQUFDL08sMERBQU1BO3dEQUFDOE8sTUFBSzt3REFBS2hILFNBQVE7OzREQUFVOzBFQUVsQyw4REFBQ2pILHFQQUFXQTtnRUFBQ3NMLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUczQiw4REFBQy9MLDhFQUFtQkE7b0RBQUM0TyxPQUFNOztzRUFDekIsOERBQUMzTywyRUFBZ0JBOzREQUFDdU8sU0FBUzlGOzs4RUFDekIsOERBQUM1SCxxUEFBU0E7b0VBQUNpTCxXQUFVOzs7Ozs7Z0VBQWlCOzs7Ozs7O3NFQUd4Qyw4REFBQzVMLGdGQUFxQkE7Ozs7O3NFQUN0Qiw4REFBQ0YsMkVBQWdCQTs0REFBQ3VPLFNBQVNsRzs7OEVBQ3pCLDhEQUFDNUgscVBBQU1BO29FQUFDcUwsV0FBVTs7Ozs7O2dFQUFpQjs7Ozs7OztzRUFHckMsOERBQUM5TCwyRUFBZ0JBOzs4RUFDZiw4REFBQ2UscVBBQVFBO29FQUFDK0ssV0FBVTs7Ozs7O2dFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRN0MsOERBQUM4QjtvQ0FBSTlCLFdBQVU7OENBQ2IsNEVBQUM4Qjt3Q0FBSTlCLFdBQVU7OzBEQUViLDhEQUFDOEI7Z0RBQUk5QixXQUFVOztrRUFFYiw4REFBQ2hNLHVFQUFZQTs7MEVBQ1gsOERBQUNHLDhFQUFtQkE7Z0VBQUN5TyxPQUFPOzBFQUMxQiw0RUFBQy9PLDBEQUFNQTtvRUFBQzhILFNBQVE7b0VBQVVnSCxNQUFLO29FQUFLM0MsV0FBVTs7c0ZBQzVDLDhEQUFDNUsscVBBQVNBOzRFQUFDNEssV0FBVTs7Ozs7O3NGQUNyQiw4REFBQ0s7NEVBQUtMLFdBQVU7c0ZBQVc7Ozs7Ozt3RUFDMUIzSixrQkFBa0IsdUJBQ2pCLDhEQUFDZ0s7NEVBQUtMLFdBQVU7c0ZBQTJGOzs7Ozs7c0ZBSTdHLDhEQUFDdEwscVBBQVdBOzRFQUFDc0wsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBRzNCLDhEQUFDL0wsOEVBQW1CQTs7a0ZBQ2xCLDhEQUFDQywyRUFBZ0JBO3dFQUFDdU8sU0FBUyxJQUFNbk0saUJBQWlCO2tGQUFROzs7Ozs7b0VBR3pEdUMsZUFBZTZELEdBQUcsQ0FBQyxDQUFDM0Qsc0JBQ25CLDhEQUFDN0UsMkVBQWdCQTs0RUFFZnVPLFNBQVMsSUFBTW5NLGlCQUFpQnlDLE1BQU02QyxFQUFFLENBQUNrSCxRQUFRO3NGQUVoRC9KLE1BQU1nSyxVQUFVOzJFQUhaaEssTUFBTTZDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQVVyQiw4REFBQ2tHO3dEQUFJOUIsV0FBVTs7MEVBRWIsOERBQUNuTSwwREFBTUE7Z0VBQ0w4TyxNQUFLO2dFQUNMaEgsU0FBUzlFLG1CQUFtQixLQUFLLFlBQVk7Z0VBQzdDNEwsU0FBUyxJQUFNM0wsa0JBQWtCO2dFQUNqQ2tKLFdBQVU7MEVBQ1g7Ozs7Ozs0REFLQXZLLGNBQWNpSCxHQUFHLENBQUMsQ0FBQ3NHO2dFQUNsQixNQUFNUixhQUFhM0wsbUJBQW1CbU0sT0FBT3ROLEtBQUs7Z0VBQ2xELHFCQUNFLDhEQUFDN0IsMERBQU1BO29FQUVMOE8sTUFBSztvRUFDTGhILFNBQVM2RyxhQUFhLFlBQVk7b0VBQ2xDQyxTQUFTLElBQU14SixtQkFBbUIrSixPQUFPdE4sS0FBSztvRUFDOUNzSyxXQUFVOztzRkFFViw4REFBQ2dELE9BQU9wTixJQUFJOzRFQUFDb0ssV0FBVTs7Ozs7O3dFQUN0QmdELE9BQU9yTixLQUFLOzttRUFQUnFOLE9BQU90TixLQUFLOzs7Ozs0REFVdkI7Ozs7Ozs7a0VBSUYsOERBQUNvTTt3REFBSTlCLFdBQVU7a0VBRVpuSyx5QkFBeUI2RyxHQUFHLENBQUMsQ0FBQ3NHOzREQUM3QixNQUFNUixhQUFhekwsOEJBQThCaU0sT0FBT3ROLEtBQUs7NERBQzdELHFCQUNFLDhEQUFDN0IsMERBQU1BO2dFQUVMOE8sTUFBSztnRUFDTGhILFNBQVM2RyxhQUFhLFlBQVk7Z0VBQ2xDQyxTQUFTLElBQU1ySiw4QkFBOEI0SixPQUFPdE4sS0FBSztnRUFDekRzSyxXQUFVOztrRkFFViw4REFBQ2dELE9BQU9wTixJQUFJO3dFQUFDb0ssV0FBVTs7Ozs7O29FQUFpQjtvRUFDcENnRCxPQUFPck4sS0FBSzs7K0RBUFhxTixPQUFPdE4sS0FBSzs7Ozs7d0RBVXZCOzs7Ozs7a0VBSUYsOERBQUMxQix1RUFBWUE7OzBFQUNYLDhEQUFDRyw4RUFBbUJBO2dFQUFDeU8sT0FBTzswRUFDMUIsNEVBQUMvTywwREFBTUE7b0VBQUM4SCxTQUFRO29FQUFVZ0gsTUFBSztvRUFBSzNDLFdBQVU7O3NGQUM1Qyw4REFBQzlLLHFQQUFRQTs0RUFBQzhLLFdBQVU7Ozs7OztzRkFDcEIsOERBQUNLOzRFQUFLTCxXQUFVO3NGQUFXOzs7Ozs7d0VBQ3pCL0ksQ0FBQUEsVUFBVUUsS0FBSyxJQUFJRixVQUFVRyxHQUFHLG1CQUNoQyw4REFBQ2lKOzRFQUFLTCxXQUFVO3NGQUEyRjs7Ozs7O3NGQUk3Ryw4REFBQ3RMLHFQQUFXQTs0RUFBQ3NMLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUczQiw4REFBQy9MLDhFQUFtQkE7Z0VBQUMrTCxXQUFVOzBFQUM3Qiw0RUFBQzhCO29FQUFJOUIsV0FBVTs7c0ZBQ2IsOERBQUM4Qjs7OEZBQ0MsOERBQUNuTTtvRkFBTXFLLFdBQVU7OEZBQXNCOzs7Ozs7OEZBQ3ZDLDhEQUFDbE0sd0RBQUtBO29GQUNKbVAsYUFBWTtvRkFDWnZOLE9BQU8yRCxrQkFBa0JwQyxVQUFVRSxLQUFLO29GQUN4QytMLFVBQVUsQ0FBQ0MsSUFBTXRKLGdCQUFnQnNKLEVBQUViLE1BQU0sQ0FBQzVNLEtBQUssRUFBRTtvRkFDakRzSyxXQUFVO29GQUNWb0QsV0FBVzs7Ozs7Ozs7Ozs7O3NGQUdmLDhEQUFDdEI7OzhGQUNDLDhEQUFDbk07b0ZBQU1xSyxXQUFVOzhGQUFzQjs7Ozs7OzhGQUN2Qyw4REFBQ2xNLHdEQUFLQTtvRkFDSm1QLGFBQVk7b0ZBQ1p2TixPQUFPMkQsa0JBQWtCcEMsVUFBVUcsR0FBRztvRkFDdEM4TCxVQUFVLENBQUNDLElBQU10SixnQkFBZ0JzSixFQUFFYixNQUFNLENBQUM1TSxLQUFLLEVBQUU7b0ZBQ2pEc0ssV0FBVTtvRkFDVm9ELFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQVN2Qiw4REFBQ3RCO2dEQUFJOUIsV0FBVTs7a0VBQ2IsOERBQUNsTSx3REFBS0E7d0RBQ0ptUCxhQUFZO3dEQUNadk4sT0FBT1M7d0RBQ1ArTSxVQUFVLENBQUNDLElBQU0vTSxlQUFlK00sRUFBRWIsTUFBTSxDQUFDNU0sS0FBSzt3REFDOUNzSyxXQUFVOzs7Ozs7a0VBRVosOERBQUNuTSwwREFBTUE7d0RBQUM4TyxNQUFLOzswRUFDWCw4REFBQy9OLHFQQUFNQTtnRUFBQ29MLFdBQVU7Ozs7Ozs0REFBaUI7Ozs7Ozs7a0VBR3JDLDhEQUFDbk0sMERBQU1BO3dEQUFDOE8sTUFBSzt3REFBS2hILFNBQVE7d0RBQVU4RyxTQUFTOUg7OzBFQUMzQyw4REFBQzlGLHFQQUFTQTtnRUFBQ21MLFdBQVU7Ozs7Ozs0REFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFRL0NuSSxlQUFlb0MsTUFBTSxLQUFLLGtCQUN6Qiw4REFBQzZIOzRCQUFJOUIsV0FBVTtzQ0FDYiw0RUFBQ3FEO2dDQUFHckQsV0FBVTswQ0FBMkI7Ozs7Ozs7Ozs7aURBRzNDLDhEQUFDOEI7NEJBQUk5QixXQUFVO3NDQUNYLDRFQUFDc0Q7Z0NBQU10RCxXQUFVOztrREFDZiw4REFBQ3VEOzswREFDQyw4REFBQ0M7Z0RBQUl4RCxXQUFVOzs7Ozs7MERBQ2YsOERBQUN3RDtnREFBSXhELFdBQVU7Ozs7OzswREFDZiw4REFBQ3dEO2dEQUFJeEQsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDd0Q7Z0RBQUl4RCxXQUFVOzs7Ozs7MERBQ2YsOERBQUN3RDtnREFBSXhELFdBQVU7Ozs7OzswREFDZiw4REFBQ3dEO2dEQUFJeEQsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDd0Q7Z0RBQUl4RCxXQUFVOzs7Ozs7MERBQ2YsOERBQUN3RDtnREFBSXhELFdBQVU7Ozs7OzswREFDZiw4REFBQ3dEO2dEQUFJeEQsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDd0Q7Z0RBQUl4RCxXQUFVOzs7Ozs7MERBQ2YsOERBQUN3RDtnREFBSXhELFdBQVU7Ozs7Ozs7Ozs7OztrREFFakIsOERBQUN5RDtrREFDQyw0RUFBQ0M7NENBQUcxRCxXQUFVOzs4REFDWiw4REFBQzJEO29EQUFHM0QsV0FBVTs4REFDWiw0RUFBQzhCO3dEQUFJOUIsV0FBVTtrRUFDYiw0RUFBQ2pNLDhEQUFRQTs0REFDUG1PLFNBQVNqTSxpQkFBaUJnRSxNQUFNLEtBQUtwQyxlQUFlb0MsTUFBTSxJQUFJcEMsZUFBZW9DLE1BQU0sR0FBRzs0REFDdEYySixpQkFBaUIzQjs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJdkIsOERBQUMwQjtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs4REFFM0QsOERBQUMyRDtvREFBRzNELFdBQVU7OERBQ1osNEVBQUM4Qjt3REFBSTlCLFdBQVU7a0VBQTBDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUkvRCw4REFBQzZEO2tEQUNFaE0sZUFBZTZFLEdBQUcsQ0FBQyxDQUFDVixTQUErQnlCO2dEQW1DbEJsRjtpRUFsQ2hDLDhEQUFDbUw7Z0RBRUMxRCxXQUFXLHFGQUlWLE9BSEMvSixpQkFBaUJzRCxRQUFRLENBQUN5QyxRQUFRSixFQUFFLElBQ2hDLCtCQUNBNkIsUUFBUSxNQUFNLElBQUksa0JBQWtCO2dEQUUxQ2dGLFNBQVMsQ0FBQ1UsSUFBTWYsbUJBQW1CcEcsUUFBUUosRUFBRSxFQUFFdUg7O2tFQUUvQyw4REFBQ1c7d0RBQUc5RCxXQUFVO2tFQUNaLDRFQUFDak0sOERBQVFBOzREQUNQbU8sU0FBU2pNLGlCQUFpQnNELFFBQVEsQ0FBQ3lDLFFBQVFKLEVBQUU7NERBQzdDZ0ksaUJBQWlCLENBQUMxQixVQUFZQyxvQkFBb0JuRyxRQUFRSixFQUFFLEVBQUVzRzs7Ozs7Ozs7Ozs7a0VBR2xFLDhEQUFDNEI7d0RBQUc5RCxXQUFVO2tFQUNaLDRFQUFDOEI7NERBQUk5QixXQUFVO3NFQUNaaEUsUUFBUStILE1BQU0saUJBQ2IsOERBQUNDO2dFQUNDQyxLQUFLakksUUFBUStILE1BQU07Z0VBQ25CRyxLQUFLbEksUUFBUWlCLGFBQWEsSUFBSTtnRUFDOUIrQyxXQUFVOzs7OztxRkFHWiw4REFBQzhCO2dFQUFJOUIsV0FBVTswRUFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSXJELDhEQUFDOEQ7d0RBQUc5RCxXQUFVO2tFQUNaLDRFQUFDOEI7NERBQUk5QixXQUFVOzs4RUFDYiw4REFBQzhCO29FQUFJOUIsV0FBVTtvRUFBeUR2RSxPQUFPTyxRQUFRaUIsYUFBYSxJQUFJOzhFQUNyR2pCLFFBQVFpQixhQUFhLElBQUk7Ozs7Ozs4RUFFNUIsOERBQUM2RTtvRUFBSTlCLFdBQVU7O3dFQUE2Qzt3RUFDckRoRSxRQUFRSixFQUFFO3dFQUFDO3dFQUFRckQsRUFBQUEsZUFBQUEsT0FBTzRMLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXhJLEVBQUUsS0FBS0ksUUFBUWpCLFFBQVEsZUFBMUN4QyxtQ0FBQUEsYUFBNkN3SyxVQUFVLEtBQUk7Ozs7Ozs7OEVBRXJGLDhEQUFDakI7b0VBQUk5QixXQUFVOzt3RUFBNkM7d0VBQ3JEaEUsUUFBUXFJLG9CQUFvQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSTNDLDhEQUFDUDt3REFBRzlELFdBQVU7a0VBQ1hILGVBQWU3RCxRQUFROUMsTUFBTTs7Ozs7O2tFQUVoQyw4REFBQzRLO3dEQUFHOUQsV0FBVTtrRUFDWiw0RUFBQzhCOzREQUFJOUIsV0FBVTs7OEVBQ2IsOERBQUM4QjtvRUFBSTlCLFdBQVU7OEVBQTBEaEUsUUFBUXNJLFlBQVksSUFBSXRJLFFBQVFlLEdBQUc7Ozs7Ozs4RUFDNUcsOERBQUMrRTtvRUFBSTlCLFdBQVU7OEVBQTBEaEUsUUFBUXVJLFlBQVksSUFBSXZJLFFBQVF3SSxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztrRUFHaEgsOERBQUNWO3dEQUFHOUQsV0FBVTtrRUFDWiw0RUFBQzhCOzREQUFJOUIsV0FBVTtzRUFBdUNoRSxRQUFReUksY0FBYyxJQUFJOzs7Ozs7Ozs7OztrRUFFbEYsOERBQUNYO3dEQUFHOUQsV0FBVTtrRUFDWiw0RUFBQzhCOzREQUFJOUIsV0FBVTtzRUFDWmhFLFFBQVEwSSxnQkFBZ0IsR0FBRyxJQUE2QixPQUF6QjFJLFFBQVEwSSxnQkFBZ0IsSUFBSzs7Ozs7Ozs7Ozs7a0VBR2pFLDhEQUFDWjt3REFBRzlELFdBQVU7a0VBQ1gyQiwwQkFDQzNGLFFBQVEwRCxZQUFZLElBQUksTUFDeEJ1Qiw2QkFBNkJsTDs7Ozs7O2tFQUdqQyw4REFBQytOO3dEQUFHOUQsV0FBVTtrRUFDWDJCLDBCQUNDM0YsUUFBUTJELGtCQUFrQixJQUFJLE1BQzlCc0IsNkJBQTZCbEw7Ozs7OztrRUFHakMsOERBQUMrTjt3REFBRzlELFdBQVU7a0VBQ1osNEVBQUM4Qjs0REFBSTlCLFdBQVU7c0VBQ1osSUFBSTFGLEtBQUswQixRQUFRMkksWUFBWSxFQUFFQyxjQUFjLENBQUM7Ozs7Ozs7Ozs7O2tFQUduRCw4REFBQ2Q7d0RBQUc5RCxXQUFVO2tFQUNaLDRFQUFDaE0sdUVBQVlBOzs4RUFDWCw4REFBQ0csOEVBQW1CQTtvRUFBQ3lPLE9BQU87OEVBQzFCLDRFQUFDL08sMERBQU1BO3dFQUFDOEgsU0FBUTt3RUFBUWdILE1BQUs7d0VBQUszQyxXQUFVO2tGQUMxQyw0RUFBQ2xMLHFQQUFjQTs0RUFBQ2tMLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBRzlCLDhEQUFDL0wsOEVBQW1CQTtvRUFBQzRPLE9BQU07O3NGQUN6Qiw4REFBQzNPLDJFQUFnQkE7NEVBQUN1TyxTQUFTLElBQU0xRyxrQkFBa0JDOzs4RkFDakQsOERBQUN6SCxxUEFBSUE7b0ZBQUN5TCxXQUFVOzs7Ozs7Z0ZBQWlCOzs7Ozs7O3NGQUduQyw4REFBQzlMLDJFQUFnQkE7OzhGQUNmLDhEQUFDTSxxUEFBSUE7b0ZBQUN3TCxXQUFVOzs7Ozs7Z0ZBQWlCOzs7Ozs7O3NGQUduQyw4REFBQzVMLGdGQUFxQkE7Ozs7O3dFQUNyQjRILFFBQVE5QyxNQUFNLEtBQUsseUJBQ2xCLDhEQUFDaEYsMkVBQWdCQTs7OEZBQ2YsOERBQUNJLHFQQUFNQTtvRkFBQzBMLFdBQVU7Ozs7OztnRkFBaUI7Ozs7Ozs7d0VBSXRDaEUsUUFBUTlDLE1BQU0sS0FBSywwQkFDbEIsOERBQUNoRiwyRUFBZ0JBOzs4RkFDZiw4REFBQ08scVBBQVNBO29GQUFDdUwsV0FBVTs7Ozs7O2dGQUFpQjs7Ozs7Ozt3RUFJekNoRSxRQUFRMEYsMkJBQTJCLEtBQUssMkJBQ3ZDOzs4RkFDRSw4REFBQ3hOLDJFQUFnQkE7b0ZBQUN1TyxTQUFTLElBQU1wQix5QkFBeUJyRixTQUFTOztzR0FDakUsOERBQUNqSCxxUEFBU0E7NEZBQUNpTCxXQUFVOzs7Ozs7d0ZBQWlCOzs7Ozs7OzhGQUd4Qyw4REFBQzlMLDJFQUFnQkE7b0ZBQUN1TyxTQUFTLElBQU1wQix5QkFBeUJyRixTQUFTOztzR0FDakUsOERBQUNoSCxxUEFBUUE7NEZBQUNnTCxXQUFVOzs7Ozs7d0ZBQWlCOzs7Ozs7Ozs7c0ZBSzNDLDhEQUFDOUwsMkVBQWdCQTs7OEZBQ2YsOERBQUNzQixxUEFBWUE7b0ZBQUN3SyxXQUFVOzs7Ozs7Z0ZBQWlCOzs7Ozs7O3NGQUczQyw4REFBQzVMLGdGQUFxQkE7Ozs7O3NGQUN0Qiw4REFBQ0YsMkVBQWdCQTs0RUFDZnVPLFNBQVMsSUFBTXZHLG9CQUFvQkYsUUFBUUosRUFBRTs0RUFDN0NvRSxXQUFVOzs4RkFFViw4REFBQ3JMLHFQQUFNQTtvRkFBQ3FMLFdBQVU7Ozs7OztnRkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBNUh0QyxHQUFpQnRJLE9BQWRzRSxRQUFRSixFQUFFLEVBQUMsS0FBYyxPQUFYbEU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBMElwQyw4REFBQ29LOzRCQUFJOUIsV0FBVTs7OENBQ2IsOERBQUM4QjtvQ0FBSTlCLFdBQVU7O3dDQUFnQzt3Q0FDdkM5SCxDQUFBQSxXQUFXMk0sSUFBSSxHQUFHLEtBQUszTSxXQUFXNE0sS0FBSyxHQUFJO3dDQUFFO3dDQUFFQyxLQUFLQyxHQUFHLENBQUM5TSxXQUFXMk0sSUFBSSxHQUFHM00sV0FBVzRNLEtBQUssRUFBRTVNLFdBQVcrTSxLQUFLO3dDQUFFO3dDQUFNL00sV0FBVytNLEtBQUs7d0NBQUM7Ozs7Ozs7OENBRTdJLDhEQUFDbkQ7b0NBQUk5QixXQUFVOztzREFDYiw4REFBQ25NLDBEQUFNQTs0Q0FDTDhPLE1BQUs7NENBQ0xoSCxTQUFROzRDQUNSdUosVUFBVWhOLFdBQVcyTSxJQUFJLElBQUk7NENBQzdCcEMsU0FBUyxJQUFNdEssb0JBQW9CO29EQUFFME0sTUFBTTNNLFdBQVcyTSxJQUFJLEdBQUc7Z0RBQUU7NENBQy9EN0UsV0FBVTtzREFDWDs7Ozs7O3dDQUtBbUYsTUFBTUMsSUFBSSxDQUFDOzRDQUFFbkwsUUFBUThLLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRCxLQUFLTSxJQUFJLENBQUNuTixXQUFXK00sS0FBSyxHQUFHL00sV0FBVzRNLEtBQUs7d0NBQUcsR0FBRyxDQUFDUSxHQUFHQzs0Q0FDdkYsTUFBTUMsVUFBVVQsS0FBS1UsR0FBRyxDQUFDLEdBQUd2TixXQUFXMk0sSUFBSSxHQUFHLEtBQUtVOzRDQUNuRCxJQUFJQyxVQUFVVCxLQUFLTSxJQUFJLENBQUNuTixXQUFXK00sS0FBSyxHQUFHL00sV0FBVzRNLEtBQUssR0FBRyxPQUFPOzRDQUVyRSxxQkFDRSw4REFBQ2pSLDBEQUFNQTtnREFFTDhPLE1BQUs7Z0RBQ0xoSCxTQUFTNkosWUFBWXROLFdBQVcyTSxJQUFJLEdBQUcsWUFBWTtnREFDbkRwQyxTQUFTLElBQU10SyxvQkFBb0I7d0RBQUUwTSxNQUFNVztvREFBUTtnREFDbkR4RixXQUFVOzBEQUVUd0Y7K0NBTklBOzs7Ozt3Q0FTWDtzREFFQSw4REFBQzNSLDBEQUFNQTs0Q0FDTDhPLE1BQUs7NENBQ0xoSCxTQUFROzRDQUNSdUosVUFBVWhOLFdBQVcyTSxJQUFJLElBQUlFLEtBQUtNLElBQUksQ0FBQ25OLFdBQVcrTSxLQUFLLEdBQUcvTSxXQUFXNE0sS0FBSzs0Q0FDMUVyQyxTQUFTLElBQU10SyxvQkFBb0I7b0RBQUUwTSxNQUFNM00sV0FBVzJNLElBQUksR0FBRztnREFBRTs0Q0FDL0Q3RSxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTVCw4REFBQ3pNLDRGQUFpQkE7Z0JBQ2hCbVMsTUFBTW5QO2dCQUNOb1AsU0FBUzFKO2dCQUNUMkosVUFBVTlKO2dCQUNWL0YsVUFBVUE7Z0JBQ1Z3QyxRQUFRQTtnQkFDUjlCLGdCQUFnQkE7Z0JBQ2hCb1AsTUFBTXBQLGlCQUFpQixTQUFTOzs7Ozs7WUFJakNULG9DQUNDLDhEQUFDeEMscUVBQWdCQTtnQkFDZmtTLE1BQU1yTztnQkFDTnlPLGNBQWN4TztnQkFDZG1FLE9BQU8sS0FBMkR6RixPQUF0RHdCLDJCQUEyQixVQUFVLE9BQU8sTUFBSyxPQUE0QixPQUF2QnhCLG1CQUFtQitHLEdBQUc7Z0JBQ3hGZ0osYUFDRXZPLDJCQUEyQixVQUN2QnhCLG1CQUFtQmlILGFBQWEsSUFBSSxLQUNwQ2pILEVBQUFBLHlDQUFBQSxtQkFBbUIySixrQkFBa0IsY0FBckMzSiw2REFBQUEsdUNBQXVDZ1EsRUFBRSxLQUFJO2dCQUVuRDdILFlBQVc7Z0JBQ1hDLGFBQWFDLDJCQUEyQnRJO2dCQUN4Q3VMLGFBQWE5SjtnQkFDYnpCLFVBQVVBO2dCQUNWbUksUUFBTyxhQUFhLFNBQVM7O2dCQUM3QitILHVCQUF1QjFFO2dCQUN2QjJFLG9CQUFvQixDQUFDQztvQkFDbkJ0SyxRQUFRN0QsS0FBSyxDQUFDLHVCQUF1Qm1PO29CQUNyQ3ZQLE1BQU07d0JBQ0o2RSxPQUFPO3dCQUNQQyxhQUFhO3dCQUNiQyxTQUFTO29CQUNYO2dCQUNGOzs7Ozs7MEJBS0osOERBQUNsSSwrRkFBd0JBO2dCQUN2QmlTLE1BQU05Tix5QkFBeUJ3TyxNQUFNO2dCQUNyQ04sY0FBY2xPLHlCQUF5QnlPLFNBQVM7Z0JBQ2hEQyxPQUFPMU8seUJBQXlCME8sS0FBSztnQkFDckNDLGNBQWMzTyx5QkFBeUIyTyxZQUFZO2dCQUNuREMsVUFBVTVPLHlCQUF5QjZPLGlCQUFpQjtnQkFDcERDLFlBQVk7b0JBQ1YsWUFBWTtvQkFDWixNQUFNdk8sb0JBQW9CO3dCQUFFb0gsSUFBSWpGLEtBQUtrRixHQUFHO29CQUFHO29CQUMzQzdILGNBQWN3QixDQUFBQSxPQUFRQSxPQUFPO29CQUM3QixvQkFBb0I7b0JBQ3BCaUMsV0FBVzt3QkFDVHhELHlCQUF5QitPLFdBQVc7b0JBQ3RDLEdBQUc7Z0JBQ0w7Ozs7Ozs7Ozs7OztBQUlSO0dBaHBDZ0I3UTs7UUFNTXpDLHFFQUFVQTtRQUNaQyxzREFBUUE7UUFpQk9JLGtHQUEyQkE7UUFZeERSLHVFQUFpQkE7UUFNakJDLHVEQUFTQTtRQU1UQywyRUFBbUJBOzs7S0FoRFQwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy91cGxvYWRwcm9kdWN0L3dvcnRlbi93b3J0ZW4tbGlzdGluZy1wYWdlLnRzeD9mZGY1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgeyB1c2VVcGxvYWRQcm9kdWN0cyB9IGZyb20gJ0AvaG9va3MvdXNlVXBsb2FkUHJvZHVjdHMnXHJcbmltcG9ydCB7IHVzZVN0b3JlcyB9IGZyb20gJ0AvaG9va3MvdXNlU3RvcmVzJ1xyXG5pbXBvcnQgeyB1c2VEcm9wc2hpcFByb2R1Y3RzIH0gZnJvbSAnQC9ob29rcy91c2VEcm9wc2hpcFByb2R1Y3RzJ1xyXG5pbXBvcnQgeyB1c2VDb25maXJtIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NvbmZpcm0tZGlhbG9nJ1xyXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0J1xyXG5pbXBvcnQgeyBVcGxvYWRQcm9kdWN0TGlzdGluZywgQ3JlYXRlVXBsb2FkUHJvZHVjdERhdGEgfSBmcm9tICdAL3R5cGVzJ1xyXG5pbXBvcnQgeyBVcGxvYWRQcm9kdWN0Rm9ybSB9IGZyb20gJ0AvY29tcG9uZW50cy91cGxvYWRwcm9kdWN0L3VwbG9hZC1wcm9kdWN0LWZvcm0nXHJcbmltcG9ydCB7IFRyYW5zbGF0aW9uTW9kYWwsIExhbmd1YWdlQ29kZSB9IGZyb20gJ0AvY29tcG9uZW50cy90cmFuc2xhdGlvbidcclxuaW1wb3J0IHsgQmF0Y2hUcmFuc2xhdGlvblByb2dyZXNzLCB1c2VCYXRjaFRyYW5zbGF0aW9uUHJvZ3Jlc3MgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmF0Y2gtdHJhbnNsYXRpb24tcHJvZ3Jlc3MnXHJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0J1xyXG5pbXBvcnQgeyBDaGVja2JveCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jaGVja2JveCdcclxuaW1wb3J0IHtcclxuICBEcm9wZG93bk1lbnUsXHJcbiAgRHJvcGRvd25NZW51Q29udGVudCxcclxuICBEcm9wZG93bk1lbnVJdGVtLFxyXG4gIERyb3Bkb3duTWVudVRyaWdnZXIsXHJcbiAgRHJvcGRvd25NZW51U2VwYXJhdG9yLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9kcm9wZG93bi1tZW51J1xyXG5cclxuaW1wb3J0IHtcclxuICBQbHVzLFxyXG4gIFVwbG9hZCxcclxuICBFZGl0LFxyXG4gIENvcHksXHJcbiAgUmVmcmVzaEN3LFxyXG4gIENoZXZyb25Eb3duLFxyXG4gIFRyYXNoMixcclxuICBTZWFyY2gsXHJcbiAgUm90YXRlQ2N3LFxyXG4gIE1vcmVIb3Jpem9udGFsLFxyXG4gIExhbmd1YWdlcyxcclxuICBGaWxlVGV4dCxcclxuICBEb3dubG9hZCxcclxuICBDYWxlbmRhcixcclxuICBTdG9yZSBhcyBTdG9yZUljb24sXHJcbiAgQ2hlY2tDaXJjbGUsXHJcbiAgQ2xvY2ssXHJcbiAgQWxlcnRDaXJjbGUsXHJcbiAgRXh0ZXJuYWxMaW5rXHJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5cclxuLy8g54q25oCB6YCJ6aG5XHJcbmNvbnN0IHN0YXR1c09wdGlvbnMgPSBbXHJcbiAgeyB2YWx1ZTogJ2RyYWZ0JywgbGFiZWw6ICfojYnnqL8nLCBpY29uOiBGaWxlVGV4dCB9LFxyXG4gIHsgdmFsdWU6ICdwZW5kaW5nJywgbGFiZWw6ICflvoXkuIrmnrYnLCBpY29uOiBDbG9jayB9LFxyXG4gIHsgdmFsdWU6ICdhY3RpdmUnLCBsYWJlbDogJ+W3suS4iuaeticsIGljb246IENoZWNrQ2lyY2xlIH0sXHJcbiAgeyB2YWx1ZTogJ2ZhaWxlZCcsIGxhYmVsOiAn5LiK5p625aSx6LSlJywgaWNvbjogQWxlcnRDaXJjbGUgfSxcclxuICB7IHZhbHVlOiAnaW5hY3RpdmUnLCBsYWJlbDogJ+W3suS4i+aeticsIGljb246IFRyYXNoMiB9LFxyXG5dXHJcblxyXG4vLyDnv7vor5HnirbmgIHpgInpoblcclxuY29uc3QgdHJhbnNsYXRpb25TdGF0dXNPcHRpb25zID0gW1xyXG4gIHsgdmFsdWU6ICdwZW5kaW5nJywgbGFiZWw6ICflvoXnv7vor5EnLCBpY29uOiBDbG9jayB9LFxyXG4gIHsgdmFsdWU6ICdjb21wbGV0ZWQnLCBsYWJlbDogJ+W3suWujOaIkCcsIGljb246IENoZWNrQ2lyY2xlIH0sXHJcbl1cclxuXHJcbmludGVyZmFjZSBVcGxvYWRQcm9kdWN0UGFnZUZ1bGxQcm9wcyB7XHJcbiAgcGxhdGZvcm06ICd3b3J0ZW4nIHwgJ3BoaCdcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFVwbG9hZFByb2R1Y3RQYWdlRnVsbCh7IHBsYXRmb3JtIH06IFVwbG9hZFByb2R1Y3RQYWdlRnVsbFByb3BzKSB7XHJcbiAgY29uc3QgW3NlbGVjdGVkUHJvZHVjdHMsIHNldFNlbGVjdGVkUHJvZHVjdHNdID0gdXNlU3RhdGU8bnVtYmVyW10+KFtdKVxyXG4gIGNvbnN0IFtzZWFyY2hWYWx1ZSwgc2V0U2VhcmNoVmFsdWVdID0gdXNlU3RhdGUoJycpXHJcbiAgY29uc3QgW3NlbGVjdGVkU3RvcmUsIHNldFNlbGVjdGVkU3RvcmVdID0gdXNlU3RhdGU8c3RyaW5nPignYWxsJylcclxuICBjb25zdCBbc2hvd0Zvcm0sIHNldFNob3dGb3JtXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtlZGl0aW5nUHJvZHVjdCwgc2V0RWRpdGluZ1Byb2R1Y3RdID0gdXNlU3RhdGU8VXBsb2FkUHJvZHVjdExpc3RpbmcgfCBudWxsPihudWxsKVxyXG4gIGNvbnN0IHsgY29uZmlybSB9ID0gdXNlQ29uZmlybSgpXHJcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxyXG5cclxuICAvLyDnrZvpgInnirbmgIEgLSDkuqflk4HnirbmgIHmlLnkuLrljZXpgIlcclxuICBjb25zdCBbc2VsZWN0ZWRTdGF0dXMsIHNldFNlbGVjdGVkU3RhdHVzXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpIC8vIOWNlemAieeKtuaAgVxyXG4gIGNvbnN0IFtzZWxlY3RlZFRyYW5zbGF0aW9uU3RhdHVzLCBzZXRTZWxlY3RlZFRyYW5zbGF0aW9uU3RhdHVzXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpIC8vIOe/u+ivkeeKtuaAgeS5n+aUueS4uuWNlemAiVxyXG4gIGNvbnN0IFtkYXRlUmFuZ2UsIHNldERhdGVSYW5nZV0gPSB1c2VTdGF0ZTx7c3RhcnQ6IHN0cmluZywgZW5kOiBzdHJpbmd9Pih7XHJcbiAgICBzdGFydDogJycsXHJcbiAgICBlbmQ6ICcnXHJcbiAgfSlcclxuXHJcbiAgLy8g57+76K+R5qih5oCB5qGG54q25oCBXHJcbiAgY29uc3QgW3RyYW5zbGF0aW9uTW9kYWxPcGVuLCBzZXRUcmFuc2xhdGlvbk1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbdHJhbnNsYXRpb25Qcm9kdWN0LCBzZXRUcmFuc2xhdGlvblByb2R1Y3RdID0gdXNlU3RhdGU8VXBsb2FkUHJvZHVjdExpc3RpbmcgfCBudWxsPihudWxsKVxyXG4gIGNvbnN0IFt0cmFuc2xhdGlvbkNvbnRlbnRUeXBlLCBzZXRUcmFuc2xhdGlvbkNvbnRlbnRUeXBlXSA9IHVzZVN0YXRlPCd0aXRsZScgfCAnZGVzY3JpcHRpb24nPigndGl0bGUnKVxyXG4gIGNvbnN0IFtyZWZyZXNoS2V5LCBzZXRSZWZyZXNoS2V5XSA9IHVzZVN0YXRlKDApIC8vIOW8uuWItuWIt+aWsGtleVxyXG5cclxuICAvLyDmibnph4/nv7vor5Hov5vluqbnrqHnkIZcclxuICBjb25zdCBiYXRjaFRyYW5zbGF0aW9uUHJvZ3Jlc3MgPSB1c2VCYXRjaFRyYW5zbGF0aW9uUHJvZ3Jlc3MoKVxyXG5cclxuICAvLyDkvb/nlKhBUEkgaG9va3NcclxuICBjb25zdCB7XHJcbiAgICB1cGxvYWRQcm9kdWN0cyxcclxuICAgIGxvYWRpbmc6IHByb2R1Y3RzTG9hZGluZyxcclxuICAgIGVycm9yOiBwcm9kdWN0c0Vycm9yLFxyXG4gICAgcGFnaW5hdGlvbixcclxuICAgIGZldGNoVXBsb2FkUHJvZHVjdHMsXHJcbiAgICBjcmVhdGVVcGxvYWRQcm9kdWN0LFxyXG4gICAgdXBkYXRlVXBsb2FkUHJvZHVjdCxcclxuICAgIGRlbGV0ZVVwbG9hZFByb2R1Y3RcclxuICB9ID0gdXNlVXBsb2FkUHJvZHVjdHMocGxhdGZvcm0pO1xyXG5cclxuICBjb25zdCB7XHJcbiAgICBzdG9yZXMsXHJcbiAgICBsb2FkaW5nOiBzdG9yZXNMb2FkaW5nLFxyXG4gICAgZmV0Y2hTdG9yZXNcclxuICB9ID0gdXNlU3RvcmVzKCk7XHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRyb3BzaGlwUHJvZHVjdHMsXHJcbiAgICBsb2FkaW5nOiBkcm9wc2hpcExvYWRpbmcsXHJcbiAgICBmZXRjaERyb3BzaGlwUHJvZHVjdHNcclxuICB9ID0gdXNlRHJvcHNoaXBQcm9kdWN0cygpO1xyXG5cclxuICAvLyDojrflj5blvZPliY3lubPlj7DnmoTlupfpk7pcclxuICBjb25zdCBwbGF0Zm9ybVN0b3JlcyA9IHN0b3Jlcy5maWx0ZXIoc3RvcmUgPT4gc3RvcmUucGxhdGZvcm1fY29kZSA9PT0gcGxhdGZvcm0pO1xyXG5cclxuICAvLyDnrZvpgInlpITnkIblh73mlbAgLSDmlLnkuLrljZXpgInmqKHlvI9cclxuICBjb25zdCBoYW5kbGVTdGF0dXNTZWxlY3QgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcclxuICAgIC8vIOWmguaenOeCueWHu+eahOaYr+W9k+WJjemAieS4reeahOeKtuaAge+8jOWImeWIh+aNouS4ulwi5YWo6YOoXCLvvIjnqbrlrZfnrKbkuLLvvIlcclxuICAgIHNldFNlbGVjdGVkU3RhdHVzKHByZXYgPT4gcHJldiA9PT0gc3RhdHVzID8gJycgOiBzdGF0dXMpXHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVUcmFuc2xhdGlvblN0YXR1c1NlbGVjdCA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xyXG4gICAgLy8g5aaC5p6c54K55Ye755qE5piv5b2T5YmN6YCJ5Lit55qE54q25oCB77yM5YiZ5YiH5o2i5Li6XCLlhajpg6hcIu+8iOepuuWtl+espuS4su+8iVxyXG4gICAgc2V0U2VsZWN0ZWRUcmFuc2xhdGlvblN0YXR1cyhwcmV2ID0+IHByZXYgPT09IHN0YXR1cyA/ICcnIDogc3RhdHVzKVxyXG4gIH1cclxuXHJcbiAgLy8g5pel5pyf5qC85byP5YyW5Ye95pWwXHJcbiAgY29uc3QgZm9ybWF0RGF0ZURpc3BsYXkgPSAoZGF0ZVN0cjogc3RyaW5nKSA9PiB7XHJcbiAgICBpZiAoIWRhdGVTdHIpIHJldHVybiAnJ1xyXG4gICAgLy8g5aaC5p6c5pivWVlZWS1NTS1EROagvOW8j++8jOi9rOaNouS4ullZWVnlubRNTeaciERE5pel5pi+56S6XHJcbiAgICBpZiAoZGF0ZVN0ci5pbmNsdWRlcygnLScpKSB7XHJcbiAgICAgIGNvbnN0IFt5ZWFyLCBtb250aCwgZGF5XSA9IGRhdGVTdHIuc3BsaXQoJy0nKVxyXG4gICAgICBpZiAoeWVhciAmJiBtb250aCAmJiBkYXkpIHtcclxuICAgICAgICByZXR1cm4gYCR7eWVhcn3lubQke21vbnRoLnBhZFN0YXJ0KDIsICcwJyl95pyIJHtkYXkucGFkU3RhcnQoMiwgJzAnKX3ml6VgXHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBkYXRlU3RyXHJcbiAgfVxyXG5cclxuICAvLyDlpITnkIbml6XmnJ/ovpPlhaVcclxuICBjb25zdCBoYW5kbGVEYXRlSW5wdXQgPSAodmFsdWU6IHN0cmluZywgdHlwZTogJ3N0YXJ0JyB8ICdlbmQnKSA9PiB7XHJcbiAgICAvLyDlj6rlhYHorrjmlbDlrZfovpPlhaVcclxuICAgIGNvbnN0IG51bWVyaWNWYWx1ZSA9IHZhbHVlLnJlcGxhY2UoL1xcRC9nLCAnJylcclxuXHJcbiAgICBpZiAobnVtZXJpY1ZhbHVlLmxlbmd0aCA8PSA4KSB7XHJcbiAgICAgIGxldCBmb3JtYXR0ZWRWYWx1ZSA9IG51bWVyaWNWYWx1ZVxyXG4gICAgICBsZXQgZGlzcGxheVZhbHVlID0gbnVtZXJpY1ZhbHVlXHJcblxyXG4gICAgICAvLyDoh6rliqjmoLzlvI/ljJbmmL7npLpcclxuICAgICAgaWYgKG51bWVyaWNWYWx1ZS5sZW5ndGggPj0gNCkge1xyXG4gICAgICAgIGNvbnN0IHllYXIgPSBudW1lcmljVmFsdWUuc2xpY2UoMCwgNClcclxuICAgICAgICBkaXNwbGF5VmFsdWUgPSBgJHt5ZWFyfeW5tGBcclxuXHJcbiAgICAgICAgaWYgKG51bWVyaWNWYWx1ZS5sZW5ndGggPj0gNikge1xyXG4gICAgICAgICAgY29uc3QgbW9udGggPSBudW1lcmljVmFsdWUuc2xpY2UoNCwgNilcclxuICAgICAgICAgIGRpc3BsYXlWYWx1ZSArPSBgJHttb250aH3mnIhgXHJcblxyXG4gICAgICAgICAgaWYgKG51bWVyaWNWYWx1ZS5sZW5ndGggPT09IDgpIHtcclxuICAgICAgICAgICAgY29uc3QgZGF5ID0gbnVtZXJpY1ZhbHVlLnNsaWNlKDYsIDgpXHJcbiAgICAgICAgICAgIGRpc3BsYXlWYWx1ZSArPSBgJHtkYXl95pelYFxyXG4gICAgICAgICAgICAvLyDovazmjaLkuLpZWVlZLU1NLURE5qC85byP5a2Y5YKoXHJcbiAgICAgICAgICAgIGZvcm1hdHRlZFZhbHVlID0gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8g5aaC5p6c6L6T5YWl5a6M5pW055qEOOS9jeaVsOWtl++8jOi9rOaNouS4uuagh+WHhuaXpeacn+agvOW8j1xyXG4gICAgICBpZiAobnVtZXJpY1ZhbHVlLmxlbmd0aCA9PT0gOCkge1xyXG4gICAgICAgIGNvbnN0IHllYXIgPSBudW1lcmljVmFsdWUuc2xpY2UoMCwgNClcclxuICAgICAgICBjb25zdCBtb250aCA9IG51bWVyaWNWYWx1ZS5zbGljZSg0LCA2KVxyXG4gICAgICAgIGNvbnN0IGRheSA9IG51bWVyaWNWYWx1ZS5zbGljZSg2LCA4KVxyXG5cclxuICAgICAgICAvLyDpqozor4Hml6XmnJ/mnInmlYjmgKdcclxuICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUocGFyc2VJbnQoeWVhciksIHBhcnNlSW50KG1vbnRoKSAtIDEsIHBhcnNlSW50KGRheSkpXHJcbiAgICAgICAgaWYgKGRhdGUuZ2V0RnVsbFllYXIoKSA9PSBwYXJzZUludCh5ZWFyKSAmJlxyXG4gICAgICAgICAgICBkYXRlLmdldE1vbnRoKCkgPT0gcGFyc2VJbnQobW9udGgpIC0gMSAmJlxyXG4gICAgICAgICAgICBkYXRlLmdldERhdGUoKSA9PSBwYXJzZUludChkYXkpKSB7XHJcbiAgICAgICAgICBmb3JtYXR0ZWRWYWx1ZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWBcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNldERhdGVSYW5nZShwcmV2ID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBbdHlwZV06IG51bWVyaWNWYWx1ZS5sZW5ndGggPT09IDggPyBmb3JtYXR0ZWRWYWx1ZSA6IG51bWVyaWNWYWx1ZVxyXG4gICAgICB9KSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZVJlc2V0ID0gKCkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRTdGF0dXMoJycpXHJcbiAgICBzZXRTZWxlY3RlZFRyYW5zbGF0aW9uU3RhdHVzKCcnKVxyXG4gICAgc2V0RGF0ZVJhbmdlKHsgc3RhcnQ6ICcnLCBlbmQ6ICcnIH0pXHJcbiAgICBzZXRTZWFyY2hWYWx1ZSgnJylcclxuICAgIHNldFNlbGVjdGVkU3RvcmUoJ2FsbCcpXHJcbiAgICAvLyDph43mlrDojrflj5bmiYDmnInkuqflk4FcclxuICAgIGZldGNoVXBsb2FkUHJvZHVjdHMoKVxyXG4gIH1cclxuXHJcbiAgLy8g5bqU55So562b6YCJXHJcbiAgY29uc3QgaGFuZGxlQXBwbHlGaWx0ZXJzID0gKCkgPT4ge1xyXG4gICAgY29uc3QgcGFyYW1zOiBhbnkgPSB7fVxyXG5cclxuICAgIGlmIChzZWFyY2hWYWx1ZSkge1xyXG4gICAgICBwYXJhbXMuc2VhcmNoID0gc2VhcmNoVmFsdWVcclxuICAgIH1cclxuXHJcbiAgICBpZiAoc2VsZWN0ZWRTdG9yZSAmJiBzZWxlY3RlZFN0b3JlICE9PSAnYWxsJykge1xyXG4gICAgICBwYXJhbXMuc3RvcmVfaWQgPSBzZWxlY3RlZFN0b3JlXHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHNlbGVjdGVkU3RhdHVzKSB7XHJcbiAgICAgIHBhcmFtcy5zdGF0dXMgPSBzZWxlY3RlZFN0YXR1c1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChzZWxlY3RlZFRyYW5zbGF0aW9uU3RhdHVzKSB7XHJcbiAgICAgIHBhcmFtcy50cmFuc2xhdGlvbl9zdGF0dXMgPSBzZWxlY3RlZFRyYW5zbGF0aW9uU3RhdHVzXHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGRhdGVSYW5nZS5zdGFydCkge1xyXG4gICAgICBwYXJhbXMuc3RhcnRfZGF0ZSA9IGRhdGVSYW5nZS5zdGFydFxyXG4gICAgfVxyXG5cclxuICAgIGlmIChkYXRlUmFuZ2UuZW5kKSB7XHJcbiAgICAgIHBhcmFtcy5lbmRfZGF0ZSA9IGRhdGVSYW5nZS5lbmRcclxuICAgIH1cclxuXHJcbiAgICBmZXRjaFVwbG9hZFByb2R1Y3RzKHBhcmFtcylcclxuICB9XHJcblxyXG4gIC8vIOebkeWQrOetm+mAieadoeS7tuWPmOWMlu+8jOiHquWKqOW6lOeUqOetm+mAiVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBoYW5kbGVBcHBseUZpbHRlcnMoKVxyXG4gICAgfSwgNTAwKSAvLyDpmLLmipblpITnkIZcclxuXHJcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKVxyXG4gIH0sIFtzZWFyY2hWYWx1ZSwgc2VsZWN0ZWRTdG9yZSwgc2VsZWN0ZWRTdGF0dXMsIHNlbGVjdGVkVHJhbnNsYXRpb25TdGF0dXMsIGRhdGVSYW5nZV0pXHJcblxyXG4gIC8vIOihqOWNleWkhOeQhuWHveaVsFxyXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZVByb2R1Y3QgPSBhc3luYyAoZGF0YTogQ3JlYXRlVXBsb2FkUHJvZHVjdERhdGEpID0+IHtcclxuICAgIGF3YWl0IGNyZWF0ZVVwbG9hZFByb2R1Y3QoZGF0YSlcclxuICAgIHNldFNob3dGb3JtKGZhbHNlKVxyXG4gIH1cclxuXHJcbiAgLy8g57yW6L6R5Lqn5ZOB5aSE55CG5Ye95pWwXHJcbiAgY29uc3QgaGFuZGxlVXBkYXRlUHJvZHVjdCA9IGFzeW5jIChkYXRhOiBhbnkpID0+IHtcclxuICAgIGlmICghZWRpdGluZ1Byb2R1Y3QpIHtcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIumUmeivr1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIue8lui+keS6p+WTgeS/oeaBr+S4jeWtmOWcqFwiLFxyXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxyXG4gICAgICB9KVxyXG4gICAgICByZXR1cm5cclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCB1cGRhdGVVcGxvYWRQcm9kdWN0KGVkaXRpbmdQcm9kdWN0LmlkLCBkYXRhKVxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwi5oiQ5YqfXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwi5Lqn5ZOB5pu05paw5oiQ5YqfXCJcclxuICAgICAgfSlcclxuICAgICAgLy8g5Yi35paw5Lqn5ZOB5YiX6KGoXHJcbiAgICAgIGF3YWl0IGZldGNoVXBsb2FkUHJvZHVjdHMoKVxyXG4gICAgICBzZXRTaG93Rm9ybShmYWxzZSlcclxuICAgICAgc2V0RWRpdGluZ1Byb2R1Y3QobnVsbClcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+abtOaWsOS6p+WTgeWksei0pTonLCBlcnJvcilcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIumUmeivr1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIuabtOaWsOS6p+WTgeWksei0pe+8jOivt+mHjeivlVwiLFxyXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxyXG4gICAgICB9KVxyXG4gICAgICB0aHJvdyBlcnJvclxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8g5qC55o2u5qih5byP6YCJ5oup5aSE55CG5Ye95pWwXHJcbiAgY29uc3QgaGFuZGxlRm9ybVN1Ym1pdCA9IGVkaXRpbmdQcm9kdWN0ID8gaGFuZGxlVXBkYXRlUHJvZHVjdCA6IGhhbmRsZUNyZWF0ZVByb2R1Y3RcclxuXHJcbiAgY29uc3QgaGFuZGxlRWRpdFByb2R1Y3QgPSAocHJvZHVjdDogVXBsb2FkUHJvZHVjdExpc3RpbmcpID0+IHtcclxuICAgIHNldEVkaXRpbmdQcm9kdWN0KHByb2R1Y3QpXHJcbiAgICBzZXRTaG93Rm9ybSh0cnVlKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlQ2xvc2VGb3JtID0gKCkgPT4ge1xyXG4gICAgc2V0U2hvd0Zvcm0oZmFsc2UpXHJcbiAgICBzZXRFZGl0aW5nUHJvZHVjdChudWxsKVxyXG4gIH1cclxuXHJcbiAgLy8g5Yid5aeL5YyW5pWw5o2uXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoU3RvcmVzKClcclxuICAgIGZldGNoVXBsb2FkUHJvZHVjdHMoKVxyXG4gICAgZmV0Y2hEcm9wc2hpcFByb2R1Y3RzKClcclxuICB9LCBbcGxhdGZvcm1dKVxyXG5cclxuICBjb25zdCBoYW5kbGVEZWxldGVQcm9kdWN0ID0gYXN5bmMgKHByb2R1Y3RJZDogbnVtYmVyKSA9PiB7XHJcbiAgICBjb25zdCBjb25maXJtZWQgPSBhd2FpdCBjb25maXJtKHtcclxuICAgICAgdGl0bGU6ICfliKDpmaTkuqflk4EnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogJ+ehruWumuimgeWIoOmZpOi/meS4quS4iuaetuS6p+WTgeWQl++8n+atpOaTjeS9nOS4jeWPr+aSpOmUgOOAgicsXHJcbiAgICAgIGNvbmZpcm1UZXh0OiAn5Yig6ZmkJyxcclxuICAgICAgY2FuY2VsVGV4dDogJ+WPlua2iCcsXHJcbiAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghY29uZmlybWVkKSB7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCBkZWxldGVVcGxvYWRQcm9kdWN0KHByb2R1Y3RJZCk7XHJcbiAgICAgIHNldFNlbGVjdGVkUHJvZHVjdHMocHJldiA9PiBwcmV2LmZpbHRlcihpZCA9PiBpZCAhPT0gcHJvZHVjdElkKSk7XHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCLkuqflk4Hlt7LmiJDlip/liKDpmaRcIixcclxuICAgICAgICB2YXJpYW50OiBcImRlZmF1bHRcIlxyXG4gICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOS6p+WTgeWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCLliKDpmaTkuqflk4Hml7blj5HnlJ/plJnor6/vvIzor7fph43or5VcIixcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCJcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVCYXRjaERlbGV0ZSA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmIChzZWxlY3RlZFByb2R1Y3RzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICBhd2FpdCBjb25maXJtKHtcclxuICAgICAgICB0aXRsZTogJ+aPkOekuicsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICfor7flhYjpgInmi6nopoHliKDpmaTnmoTkuqflk4EnLFxyXG4gICAgICAgIGNvbmZpcm1UZXh0OiAn55+l6YGT5LqGJyxcclxuICAgICAgICB2YXJpYW50OiAnaW5mbycsXHJcbiAgICAgICAgaWNvbjogdHJ1ZVxyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNvbmZpcm1lZCA9IGF3YWl0IGNvbmZpcm0oe1xyXG4gICAgICB0aXRsZTogJ+aJuemHj+WIoOmZpCcsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBg56Gu5a6a6KaB5Yig6Zmk6YCJ5Lit55qEICR7c2VsZWN0ZWRQcm9kdWN0cy5sZW5ndGh9IOS4quS6p+WTgeWQl++8n+atpOaTjeS9nOS4jeWPr+aSpOmUgOOAgmAsXHJcbiAgICAgIGNvbmZpcm1UZXh0OiBg5Yig6ZmkICR7c2VsZWN0ZWRQcm9kdWN0cy5sZW5ndGh9IOS4quS6p+WTgWAsXHJcbiAgICAgIGNhbmNlbFRleHQ6ICflj5bmtognLFxyXG4gICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIWNvbmZpcm1lZCkge1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5om56YeP5Yig6Zmk5Lqn5ZOBXHJcbiAgICAgIGF3YWl0IFByb21pc2UuYWxsKHNlbGVjdGVkUHJvZHVjdHMubWFwKGlkID0+IGRlbGV0ZVVwbG9hZFByb2R1Y3QoaWQpKSk7XHJcbiAgICAgIHNldFNlbGVjdGVkUHJvZHVjdHMoW10pO1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgZGVzY3JpcHRpb246IGDlt7LmiJDlip/liKDpmaQgJHtzZWxlY3RlZFByb2R1Y3RzLmxlbmd0aH0g5Liq5Lqn5ZOBYCxcclxuICAgICAgICB2YXJpYW50OiBcImRlZmF1bHRcIlxyXG4gICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJuemHj+WIoOmZpOWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCLliKDpmaTkuqflk4Hml7blj5HnlJ/plJnor6/vvIzor7fph43or5VcIixcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCJcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVCYXRjaFRyYW5zbGF0aW9uID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKHNlbGVjdGVkUHJvZHVjdHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIGF3YWl0IGNvbmZpcm0oe1xyXG4gICAgICAgIHRpdGxlOiAn5o+Q56S6JyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+WFiOmAieaLqeimgee/u+ivkeeahOS6p+WTgScsXHJcbiAgICAgICAgY29uZmlybVRleHQ6ICfnn6XpgZPkuoYnLFxyXG4gICAgICAgIHZhcmlhbnQ6ICdpbmZvJyxcclxuICAgICAgICBpY29uOiB0cnVlXHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY29uZmlybWVkID0gYXdhaXQgY29uZmlybSh7XHJcbiAgICAgIHRpdGxlOiAn5om56YeP57+76K+RJyxcclxuICAgICAgZGVzY3JpcHRpb246IGDnoa7lrpropoHnv7vor5HpgInkuK3nmoQgJHtzZWxlY3RlZFByb2R1Y3RzLmxlbmd0aH0g5Liq5Lqn5ZOB5ZCX77yf5bCG57+76K+R5Lqn5ZOB55qE5qCH6aKY44CB5o+P6L+w5ZKM5Y2W54K544CCYCxcclxuICAgICAgY29uZmlybVRleHQ6IGDnv7vor5EgJHtzZWxlY3RlZFByb2R1Y3RzLmxlbmd0aH0g5Liq5Lqn5ZOBYCxcclxuICAgICAgY2FuY2VsVGV4dDogJ+WPlua2iCcsXHJcbiAgICAgIHZhcmlhbnQ6ICdkZWZhdWx0J1xyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCFjb25maXJtZWQpIHtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIOWHhuWkh+i/m+W6puaVsOaNrlxyXG4gICAgY29uc3Qgc2VsZWN0ZWRQcm9kdWN0c0RhdGEgPSB1cGxvYWRQcm9kdWN0cy5maWx0ZXIocCA9PiBzZWxlY3RlZFByb2R1Y3RzLmluY2x1ZGVzKHAuaWQpKVxyXG4gICAgY29uc3QgcHJvZ3Jlc3NJdGVtcyA9IHNlbGVjdGVkUHJvZHVjdHNEYXRhLm1hcChwcm9kdWN0ID0+ICh7XHJcbiAgICAgIGlkOiBwcm9kdWN0LmlkLFxyXG4gICAgICBza3U6IHByb2R1Y3Quc2t1LFxyXG4gICAgICBuYW1lOiBwcm9kdWN0LmVuZ2xpc2hfdGl0bGUgfHwgYOS6p+WTgSAke3Byb2R1Y3QuaWR9YFxyXG4gICAgfSkpXHJcblxyXG4gICAgLy8g5ZCv5Yqo6L+b5bqm5a+56K+d5qGGXHJcbiAgICBiYXRjaFRyYW5zbGF0aW9uUHJvZ3Jlc3Muc3RhcnRUcmFuc2xhdGlvbihwcm9ncmVzc0l0ZW1zKVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOiOt+WPluiupOivgSB0b2tlblxyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhdXRoX3Rva2VuJyk7XHJcbiAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+WFiOeZu+W9lScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDmqKHmi5/pgJDkuKrkuqflk4Hnv7vor5Hov5vluqbvvIjlrp7pmYXkuIrlkI7nq6/mmK/mibnph4/lpITnkIbnmoTvvIlcclxuICAgICAgc2VsZWN0ZWRQcm9kdWN0cy5mb3JFYWNoKChwcm9kdWN0SWQsIGluZGV4KSA9PiB7XHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICBiYXRjaFRyYW5zbGF0aW9uUHJvZ3Jlc3Muc2V0UHJvY2Vzc2luZ0l0ZW0ocHJvZHVjdElkKVxyXG4gICAgICAgIH0sIGluZGV4ICogMTAwKSAvLyDmr48xMDBtc+agh+iusOS4gOS4quS6p+WTgeS4uuWkhOeQhuS4rVxyXG4gICAgICB9KVxyXG5cclxuICAgICAgLy8g6LCD55So5om56YeP57+76K+RIEFQSVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3YxL3RyYW5zbGF0aW9uL2JhdGNoL2ZvcmJhdGNoJywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgcGxhdGZvcm06ICd3b3J0ZW4nLFxyXG4gICAgICAgICAgc291cmNlOiAnZm9ybV9iYXRjaCcsIC8vIOaJuemHj+e/u+ivkeWcuuaZr1xyXG4gICAgICAgICAgc291cmNlTGFuZzogJ2VuJywgLy8g6buY6K6k5LuO6Iux6K+t57+76K+RXHJcbiAgICAgICAgICB0YXJnZXRMYW5nczogZ2V0UGxhdGZvcm1UYXJnZXRMYW5ndWFnZXMoJ3dvcnRlbicpLCAvLyDmoLnmja7lubPlj7Dojrflj5bnm67moIfor63oqIBcclxuICAgICAgICAgIHByb2R1Y3RpZHM6IHNlbGVjdGVkUHJvZHVjdHMuam9pbignLCcpXHJcbiAgICAgICAgfSksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcign5om56YeP57+76K+R6K+35rGC5aSx6LSlJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIC8vIOajgOafpeWTjeW6lOeKtuaAgSAtIOWQjuerr+i/lOWbniBjb2RlOiAyMDAg6KGo56S65oiQ5YqfXHJcbiAgICAgIGlmIChyZXN1bHQuY29kZSA9PT0gMjAwICYmIHJlc3VsdC5kYXRhPy5zdWNjZXNzKSB7XHJcbiAgICAgICAgLy8g6Kej5p6Q57+76K+R57uT5p6cXHJcbiAgICAgICAgY29uc3QgeyByZXN1bHRzIH0gPSByZXN1bHQuZGF0YTtcclxuICAgICAgICBjb25zdCBzdWNjZXNzQ291bnQgPSByZXN1bHRzPy5zdWNjZXNzZnVsIHx8IDA7XHJcbiAgICAgICAgY29uc3QgZmFpbGVkQ291bnQgPSByZXN1bHRzPy5mYWlsZWQgfHwgMDtcclxuICAgICAgICBjb25zdCBkZXRhaWxzID0gcmVzdWx0cz8uZGV0YWlscyB8fCBbXTtcclxuXHJcbiAgICAgICAgLy8g5pu05paw5q+P5Liq5Lqn5ZOB55qE6L+b5bqm54q25oCBXHJcbiAgICAgICAgZGV0YWlscy5mb3JFYWNoKChkZXRhaWw6IGFueSkgPT4ge1xyXG4gICAgICAgICAgaWYgKGRldGFpbC5zdWNjZXNzKSB7XHJcbiAgICAgICAgICAgIGJhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcy5zZXRTdWNjZXNzSXRlbShkZXRhaWwucHJvZHVjdElkLCAn57+76K+R5a6M5oiQJylcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGJhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcy5zZXRFcnJvckl0ZW0oZGV0YWlsLnByb2R1Y3RJZCwgZGV0YWlsLmVycm9yIHx8ICfnv7vor5HlpLHotKUnKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgICAgIC8vIOagh+iusOe/u+ivkeWujOaIkFxyXG4gICAgICAgIGJhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcy5maW5pc2hUcmFuc2xhdGlvbigpXHJcblxyXG4gICAgICAgIHNldFNlbGVjdGVkUHJvZHVjdHMoW10pO1xyXG5cclxuICAgICAgICBpZiAoc3VjY2Vzc0NvdW50ID4gMCkge1xyXG4gICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICB0aXRsZTogJ+aJuemHj+e/u+ivkeWujOaIkCcsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBg5oiQ5Yqf57+76K+RICR7c3VjY2Vzc0NvdW50fSDkuKrkuqflk4Eke2ZhaWxlZENvdW50ID4gMCA/IGDvvIzlpLHotKUgJHtmYWlsZWRDb3VudH0g5LiqYCA6ICcnfWAsXHJcbiAgICAgICAgICAgIHZhcmlhbnQ6IFwiZGVmYXVsdFwiXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICB0aXRsZTogJ+aJuemHj+e/u+ivkeWksei0pScsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBg5omA5pyJ5Lqn5ZOB57+76K+R5aSx6LSl77yM6K+35qOA5p+l5Lqn5ZOB5pWw5o2u5ZCO6YeN6K+VYCxcclxuICAgICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIOW8uuWItuWIt+aWsOS6p+WTgeWIl+ihqO+8jOehruS/neaYvuekuuacgOaWsOeahOe/u+ivkeeKtuaAgVxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfmibnph4/nv7vor5HlrozmiJDvvIzlvIDlp4vliLfmlrDmlbDmja4uLi4nKTtcclxuXHJcbiAgICAgICAgLy8g5riF6Zmk57yT5a2Y5bm26YeN5paw6I635Y+W5pWw5o2uXHJcbiAgICAgICAgYXdhaXQgZmV0Y2hVcGxvYWRQcm9kdWN0cyh7XHJcbiAgICAgICAgICBfdDogRGF0ZS5ub3coKSxcclxuICAgICAgICAgIGZvcmNlX3JlZnJlc2g6IHRydWVcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ+esrOS4gOasoeWIt+aWsOWujOaIkO+8jOW9k+WJjee7hOS7tueKtuaAgeS4reeahOS6p+WTgeaVsOaNrjonLCB1cGxvYWRQcm9kdWN0cy5zbGljZSgwLCAyKS5tYXAocCA9PiAoe1xyXG4gICAgICAgICAgaWQ6IHAuaWQsXHJcbiAgICAgICAgICBza3U6IHAuc2t1LFxyXG4gICAgICAgICAgbXVsdGlfdGl0bGVzOiBwLm11bHRpX3RpdGxlcyxcclxuICAgICAgICAgIG11bHRpX2Rlc2NyaXB0aW9uczogcC5tdWx0aV9kZXNjcmlwdGlvbnNcclxuICAgICAgICB9KSkpO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygn5Yi35paw5a6M5oiQ77yMcmVmcmVzaEtleTonLCByZWZyZXNoS2V5KTtcclxuXHJcbiAgICAgICAgLy8g5by65Yi26YeN5paw5riy5p+T57uE5Lu2XHJcbiAgICAgICAgc2V0UmVmcmVzaEtleShwcmV2ID0+IHByZXYgKyAxKTtcclxuXHJcbiAgICAgICAgLy8g5aaC5p6c6L+Y5piv5rKh5pyJ5Yi35paw77yM5YaN5qyh5bCd6K+VXHJcbiAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn5bu26L+f5Yi35paw5byA5aeLLi4uJyk7XHJcbiAgICAgICAgICBhd2FpdCBmZXRjaFVwbG9hZFByb2R1Y3RzKHsgX3Q6IERhdGUubm93KCkgfSk7XHJcbiAgICAgICAgICBzZXRSZWZyZXNoS2V5KHByZXYgPT4gcHJldiArIDEpO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ+W7tui/n+WIt+aWsOWujOaIkCcpO1xyXG4gICAgICAgIH0sIDEwMDApO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXN1bHQuZGF0YT8ubWVzc2FnZSB8fCByZXN1bHQubWVzc2FnZSB8fCAn5om56YeP57+76K+R5aSx6LSlJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJuemHj+e/u+ivkeWksei0pTonLCBlcnJvcik7XHJcblxyXG4gICAgICAvLyDmoIforrDmiYDmnInkuqflk4HkuLrlpLHotKVcclxuICAgICAgc2VsZWN0ZWRQcm9kdWN0cy5mb3JFYWNoKHByb2R1Y3RJZCA9PiB7XHJcbiAgICAgICAgYmF0Y2hUcmFuc2xhdGlvblByb2dyZXNzLnNldEVycm9ySXRlbShwcm9kdWN0SWQsIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ+e/u+ivkeWksei0pScpXHJcbiAgICAgIH0pXHJcblxyXG4gICAgICAvLyDmoIforrDnv7vor5HlrozmiJBcclxuICAgICAgYmF0Y2hUcmFuc2xhdGlvblByb2dyZXNzLmZpbmlzaFRyYW5zbGF0aW9uKClcclxuXHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIuaJuemHj+e/u+ivkeaXtuWPkeeUn+mUmeivr++8jOivt+mHjeivlVwiLFxyXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGdldFN0YXR1c0JhZGdlID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBzdGF0dXNNYXAgPSB7XHJcbiAgICAgIGRyYWZ0OiB7IGxhYmVsOiAn6I2J56i/JywgY2xhc3NOYW1lOiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCcgfSxcclxuICAgICAgcGVuZGluZzogeyBsYWJlbDogJ+W+heS4iuaeticsIGNsYXNzTmFtZTogJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJyB9LFxyXG4gICAgICBhY3RpdmU6IHsgbGFiZWw6ICflt7LkuIrmnrYnLCBjbGFzc05hbWU6ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIH0sXHJcbiAgICAgIGZhaWxlZDogeyBsYWJlbDogJ+S4iuaetuWksei0pScsIGNsYXNzTmFtZTogJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJyB9LFxyXG4gICAgICBpbmFjdGl2ZTogeyBsYWJlbDogJ+W3suS4i+aeticsIGNsYXNzTmFtZTogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnIH1cclxuICAgIH1cclxuICAgIGNvbnN0IGNvbmZpZyA9IHN0YXR1c01hcFtzdGF0dXMgYXMga2V5b2YgdHlwZW9mIHN0YXR1c01hcF0gfHwgc3RhdHVzTWFwLmRyYWZ0XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMS41IHB5LTAuNSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtjb25maWcuY2xhc3NOYW1lfWB9PlxyXG4gICAgICAgIHtjb25maWcubGFiZWx9XHJcbiAgICAgIDwvc3Bhbj5cclxuICAgIClcclxuICB9XHJcblxyXG4gIGNvbnN0IGdldFRyYW5zbGF0aW9uU3RhdHVzQmFkZ2UgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnN0IHN0YXR1c01hcCA9IHtcclxuICAgICAgcGVuZGluZzogeyBsYWJlbDogJ+W+hee/u+ivkScsIGNsYXNzTmFtZTogJ2JnLW9yYW5nZS0xMDAgdGV4dC1vcmFuZ2UtODAwJyB9LFxyXG4gICAgICBjb21wbGV0ZWQ6IHsgbGFiZWw6ICflt7LlrozmiJAnLCBjbGFzc05hbWU6ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIH1cclxuICAgIH1cclxuICAgIGNvbnN0IGNvbmZpZyA9IHN0YXR1c01hcFtzdGF0dXMgYXMga2V5b2YgdHlwZW9mIHN0YXR1c01hcF0gfHwgc3RhdHVzTWFwLnBlbmRpbmdcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtjb25maWcuY2xhc3NOYW1lfWB9PlxyXG4gICAgICAgIHtjb25maWcubGFiZWx9XHJcbiAgICAgIDwvc3Bhbj5cclxuICAgIClcclxuICB9XHJcblxyXG4gIC8vIOiOt+WPluW5s+WPsOaYvuekuuWQjeensFxyXG4gIGNvbnN0IGdldFBsYXRmb3JtTmFtZSA9IChwbGF0Zm9ybUNvZGU6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgcGxhdGZvcm1NYXA6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XHJcbiAgICAgIHdvcnRlbjogJ1dvcnRlbicsXHJcbiAgICAgIHBoaDogJ1BISCcsXHJcbiAgICAgIGFtYXpvbjogJ0FtYXpvbicsXHJcbiAgICAgIGViYXk6ICdlQmF5JyxcclxuICAgICAgc2hvcGlmeTogJ1Nob3BpZnknXHJcbiAgICB9XHJcbiAgICByZXR1cm4gcGxhdGZvcm1NYXBbcGxhdGZvcm1Db2RlXSB8fCBwbGF0Zm9ybUNvZGUudG9VcHBlckNhc2UoKVxyXG4gIH1cclxuXHJcbiAgLy8g6I635Y+W5bmz5Y+w5omA6ZyA55qE6K+t6KiA5YiX6KGoXHJcbiAgY29uc3QgZ2V0UGxhdGZvcm1SZXF1aXJlZExhbmd1YWdlcyA9IChwbGF0Zm9ybUNvZGU6IHN0cmluZyk6IHN0cmluZ1tdID0+IHtcclxuICAgIGNvbnN0IHBsYXRmb3JtTGFuZ3VhZ2VzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmdbXT4gPSB7XHJcbiAgICAgIHdvcnRlbjogWydQVCcsICdFUyddLCAvLyBXb3J0ZW7pnIDopoHokaHokITniZnor63lkozopb/nj63niZnor61cclxuICAgICAgcGhoOiBbJ0xUJywgJ0xWJywgJ0VFJywgJ0ZJJ10sIC8vIFBISOmcgOimgeeri+mZtuWum+ivreOAgeaLieiEsee7tOS6muivreOAgeeIseaymeWwvOS6muivreOAgeiKrOWFsOivrVxyXG4gICAgICBhbWF6b246IFsnRU4nLCAnREUnLCAnRlInLCAnSVQnLCAnRVMnXSxcclxuICAgICAgZWJheTogWydFTiddLFxyXG4gICAgICBzaG9waWZ5OiBbJ0VOJ11cclxuICAgIH1cclxuICAgIHJldHVybiBwbGF0Zm9ybUxhbmd1YWdlc1twbGF0Zm9ybUNvZGVdIHx8IFsnRU4nXVxyXG4gIH1cclxuXHJcbiAgLy8g6I635Y+W5bmz5Y+w57+76K+R55uu5qCH6K+t6KiA77yI6L2s5o2i5Li6TGFuZ3VhZ2VDb2Rl5qC85byP77yJXHJcbiAgY29uc3QgZ2V0UGxhdGZvcm1UYXJnZXRMYW5ndWFnZXMgPSAocGxhdGZvcm1Db2RlOiBzdHJpbmcpOiBMYW5ndWFnZUNvZGVbXSA9PiB7XHJcbiAgICBjb25zdCBsYW5ndWFnZU1hcDogUmVjb3JkPHN0cmluZywgTGFuZ3VhZ2VDb2RlPiA9IHtcclxuICAgICAgJ1BUJzogJ3B0JyxcclxuICAgICAgJ0VTJzogJ2VzJyxcclxuICAgICAgJ0xUJzogJ2x0JyxcclxuICAgICAgJ0xWJzogJ2x2JyxcclxuICAgICAgJ0VFJzogJ2V0JywgLy8g54ix5rKZ5bC85Lqa6K+tXHJcbiAgICAgICdGSSc6ICdmaScsXHJcbiAgICAgICdFTic6ICdlbicsXHJcbiAgICAgICdERSc6ICd6aCcsIC8vIOaaguaXtuaYoOWwhOWIsOS4reaWh++8jOWunumZhemhueebruS4remcgOimgeaUr+aMgeW+t+ivrVxyXG4gICAgICAnRlInOiAnemgnLCAvLyDmmoLml7bmmKDlsITliLDkuK3mlofvvIzlrp7pmYXpobnnm67kuK3pnIDopoHmlK/mjIHms5Xor61cclxuICAgICAgJ0lUJzogJ3poJyAgLy8g5pqC5pe25pig5bCE5Yiw5Lit5paH77yM5a6e6ZmF6aG555uu5Lit6ZyA6KaB5pSv5oyB5oSP5aSn5Yip6K+tXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcGxhdGZvcm1MYW5ndWFnZXMgPSBnZXRQbGF0Zm9ybVJlcXVpcmVkTGFuZ3VhZ2VzKHBsYXRmb3JtQ29kZSlcclxuICAgIHJldHVybiBwbGF0Zm9ybUxhbmd1YWdlcy5tYXAobGFuZyA9PiBsYW5ndWFnZU1hcFtsYW5nXSB8fCAnZW4nIGFzIExhbmd1YWdlQ29kZSlcclxuICB9XHJcblxyXG4gIC8vIOWNleS4quS6p+WTgee/u+ivkeWkhOeQhlxyXG4gIGNvbnN0IGhhbmRsZVByb2R1Y3RUcmFuc2xhdGlvbiA9IChwcm9kdWN0OiBVcGxvYWRQcm9kdWN0TGlzdGluZywgY29udGVudFR5cGU6ICd0aXRsZScgfCAnZGVzY3JpcHRpb24nKSA9PiB7XHJcbiAgICBzZXRUcmFuc2xhdGlvblByb2R1Y3QocHJvZHVjdClcclxuICAgIHNldFRyYW5zbGF0aW9uQ29udGVudFR5cGUoY29udGVudFR5cGUpXHJcbiAgICBzZXRUcmFuc2xhdGlvbk1vZGFsT3Blbih0cnVlKVxyXG4gIH1cclxuXHJcbiAgLy8g5om56YeP57+76K+R5Yqf6IO95bey56e76Zmk77yM5L2/55So5Y2V5Liq5Lqn5ZOB57+76K+RXHJcblxyXG4gIC8vIOe/u+ivkeWujOaIkOWkhOeQhlxyXG4gIGNvbnN0IGhhbmRsZVRyYW5zbGF0aW9uQ29tcGxldGUgPSBhc3luYyAodHJhbnNsYXRpb25zOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+KSA9PiB7XHJcbiAgICBpZiAoIXRyYW5zbGF0aW9uUHJvZHVjdCkgcmV0dXJuXHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5p6E5bu65pu05paw5pWw5o2uXHJcbiAgICAgIGNvbnN0IHVwZGF0ZURhdGE6IGFueSA9IHt9XHJcblxyXG4gICAgICBpZiAodHJhbnNsYXRpb25Db250ZW50VHlwZSA9PT0gJ3RpdGxlJykge1xyXG4gICAgICAgIHVwZGF0ZURhdGEubXVsdGlfdGl0bGVzID0ge1xyXG4gICAgICAgICAgLi4uKHRyYW5zbGF0aW9uUHJvZHVjdC5tdWx0aV90aXRsZXMgfHwge30pLFxyXG4gICAgICAgICAgLi4udHJhbnNsYXRpb25zXHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHVwZGF0ZURhdGEubXVsdGlfZGVzY3JpcHRpb25zID0ge1xyXG4gICAgICAgICAgLi4uKHRyYW5zbGF0aW9uUHJvZHVjdC5tdWx0aV9kZXNjcmlwdGlvbnMgfHwge30pLFxyXG4gICAgICAgICAgLi4udHJhbnNsYXRpb25zXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDmm7TmlrDnv7vor5HnirbmgIFcclxuICAgICAgdXBkYXRlRGF0YS5saXN0aW5nc190cmFuc2xhdGlvbl9zdGF0dXMgPSAnY29tcGxldGVkJ1xyXG5cclxuICAgICAgLy8g6LCD55So5pu05pawQVBJXHJcbiAgICAgIGF3YWl0IHVwZGF0ZVVwbG9hZFByb2R1Y3QodHJhbnNsYXRpb25Qcm9kdWN0LmlkLCB1cGRhdGVEYXRhKVxyXG5cclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiAn57+76K+R5a6M5oiQJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogYCR7dHJhbnNsYXRpb25Db250ZW50VHlwZSA9PT0gJ3RpdGxlJyA/ICfmoIfpopgnIDogJ+aPj+i/sCd957+76K+R5bey5L+d5a2YYFxyXG4gICAgICB9KVxyXG5cclxuICAgICAgLy8g5Yi35paw5pWw5o2uXHJcbiAgICAgIGF3YWl0IGZldGNoVXBsb2FkUHJvZHVjdHMoKVxyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1RyYW5zbGF0aW9uIHNhdmUgZXJyb3I6JywgZXJyb3IpXHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogJ+S/neWtmOWksei0pScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICfnv7vor5Hnu5Pmnpzkv53lrZjlpLHotKXvvIzor7fph43or5UnLFxyXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcclxuICAgICAgfSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIOa4suafk+WkmuivreiogOeKtuaAgee7hOS7tiAtIOerluedgOaOkuWIl++8jOe0p+WHkeagt+W8j1xyXG4gIGNvbnN0IHJlbmRlck11bHRpTGFuZ3VhZ2VTdGF0dXMgPSAobXVsdGlMYW5nRGF0YTogUmVjb3JkPHN0cmluZywgc3RyaW5nPiB8IG51bGwsIHJlcXVpcmVkTGFuZ3VhZ2VzOiBzdHJpbmdbXSkgPT4ge1xyXG4gICAgY29uc3QgZGF0YSA9IG11bHRpTGFuZ0RhdGEgfHwge31cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHNwYWNlLXktMVwiPlxyXG4gICAgICAgIHtyZXF1aXJlZExhbmd1YWdlcy5tYXAobGFuZyA9PiB7XHJcbiAgICAgICAgICBjb25zdCBoYXNUcmFuc2xhdGlvbiA9IGRhdGFbbGFuZ10gJiYgZGF0YVtsYW5nXS50cmltKCkgIT09ICcnXHJcbiAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAga2V5PXtsYW5nfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTEgcHktMC41IHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWNlbnRlciB3LTggJHtcclxuICAgICAgICAgICAgICAgIGhhc1RyYW5zbGF0aW9uXHJcbiAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTcwMCdcclxuICAgICAgICAgICAgICAgICAgOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC03MDAnXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7bGFuZ31cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApXHJcbiAgICAgICAgfSl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlU2VsZWN0QWxsID0gKGNoZWNrZWQ6IGJvb2xlYW4pID0+IHtcclxuICAgIGlmIChjaGVja2VkKSB7XHJcbiAgICAgIHNldFNlbGVjdGVkUHJvZHVjdHModXBsb2FkUHJvZHVjdHMubWFwKChwOiBVcGxvYWRQcm9kdWN0TGlzdGluZykgPT4gcC5pZCkpXHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRTZWxlY3RlZFByb2R1Y3RzKFtdKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlU2VsZWN0UHJvZHVjdCA9IChwcm9kdWN0SWQ6IG51bWJlciwgY2hlY2tlZDogYm9vbGVhbikgPT4ge1xyXG4gICAgaWYgKGNoZWNrZWQpIHtcclxuICAgICAgc2V0U2VsZWN0ZWRQcm9kdWN0cyhbLi4uc2VsZWN0ZWRQcm9kdWN0cywgcHJvZHVjdElkXSlcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldFNlbGVjdGVkUHJvZHVjdHMoc2VsZWN0ZWRQcm9kdWN0cy5maWx0ZXIoaWQgPT4gaWQgIT09IHByb2R1Y3RJZCkpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyDlpITnkIbkuqflk4HooYzljZXlh7vpgInkuK1cclxuICBjb25zdCBoYW5kbGVQcm9kdWN0Q2xpY2sgPSAocHJvZHVjdElkOiBudW1iZXIsIGV2ZW50OiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XHJcbiAgICAvLyDpmLLmraLlpI3pgInmoYbngrnlh7vop6blj5HooYzngrnlh7tcclxuICAgIGlmICgoZXZlbnQudGFyZ2V0IGFzIEhUTUxFbGVtZW50KS5jbG9zZXN0KCdpbnB1dFt0eXBlPVwiY2hlY2tib3hcIl0nKSB8fFxyXG4gICAgICAgIChldmVudC50YXJnZXQgYXMgSFRNTEVsZW1lbnQpLmNsb3Nlc3QoJ2J1dHRvbicpKSB7XHJcbiAgICAgIHJldHVyblxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZFByb2R1Y3RzLmluY2x1ZGVzKHByb2R1Y3RJZClcclxuICAgIGhhbmRsZVNlbGVjdFByb2R1Y3QocHJvZHVjdElkLCAhaXNTZWxlY3RlZClcclxuICB9XHJcblxyXG4gIC8vIOaYvuekuuWKoOi9veeKtuaAgVxyXG4gIGlmIChwcm9kdWN0c0xvYWRpbmcgJiYgdXBsb2FkUHJvZHVjdHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPuWKoOi9veS4rS4uLjwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLy8g5pi+56S66ZSZ6K+v54q25oCBXHJcbiAgaWYgKHByb2R1Y3RzRXJyb3IpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+5Yqg6L295aSx6LSlOiB7cHJvZHVjdHNFcnJvcn08L3A+XHJcbiAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IGZldGNoVXBsb2FkUHJvZHVjdHMoKX0gdmFyaWFudD1cIm91dGxpbmVcIj5cclxuICAgICAgICAgICAg6YeN6K+VXHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgcGxhdGZvcm1OYW1lID0gZ2V0UGxhdGZvcm1OYW1lKHBsYXRmb3JtKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLVtjYWxjKDEwMHZoLTRyZW0pXVwiPlxyXG4gICAgICA8Q2FyZCBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbFwiPlxyXG4gICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTAgZmxleC0xIGZsZXggZmxleC1jb2xcIj5cclxuICAgICAgICAgIHsvKiDlt6XlhbfmoI8gKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIHAtNFwiPlxyXG4gICAgICAgICAgICB7Lyog5Li76KaB5pON5L2c5oyJ6ZKuICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIG1iLTRcIj5cclxuICAgICAgICAgICAgICA8QnV0dG9uIHNpemU9XCJzbVwiIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gc2V0U2hvd0Zvcm0odHJ1ZSl9PlxyXG4gICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cclxuICAgICAgICAgICAgICAgIOaWsOWinuS4iuaetlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxCdXR0b24gc2l6ZT1cInNtXCIgdmFyaWFudD1cIm91dGxpbmVcIj5cclxuICAgICAgICAgICAgICAgIDxVcGxvYWQgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cclxuICAgICAgICAgICAgICAgIOaJuemHj+S4iuaetlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIHsvKiDmibnph4/nv7vor5Hlip/og73lt7Lnp7vpmaTvvIzkvb/nlKjljZXkuKrkuqflk4HnmoTnv7vor5HmjInpkq4gKi99XHJcbiAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwic21cIiB2YXJpYW50PVwib3V0bGluZVwiPlxyXG4gICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiAvPlxyXG4gICAgICAgICAgICAgICAg5ZCM5q2l54q25oCBXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgey8qIOaJuemHj+aTjeS9nOS4i+aLieiPnOWNlSAqL31cclxuICAgICAgICAgICAgICA8RHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwic21cIiB2YXJpYW50PVwib3V0bGluZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIOaJuemHj+aTjeS9nFxyXG4gICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9XCJ3LTQgaC00IG1sLTFcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51VHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50IGFsaWduPVwic3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gb25DbGljaz17aGFuZGxlQmF0Y2hUcmFuc2xhdGlvbn0+XHJcbiAgICAgICAgICAgICAgICAgICAgPExhbmd1YWdlcyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIOaJuemHj+e/u+ivkVxyXG4gICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVTZXBhcmF0b3IgLz5cclxuICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gb25DbGljaz17aGFuZGxlQmF0Y2hEZWxldGV9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICDmibnph4/liKDpmaRcclxuICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICDmibnph4/lr7zlh7pcclxuICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHsvKiDnrZvpgInlkozmkJzntKLljLrln58gKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtNCBib3JkZXItdFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICB7Lyog5bem5L6n77ya562b6YCJ6I+c5Y2VICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICB7Lyog5bqX6ZO6562b6YCJICovfVxyXG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidy0zMiBqdXN0aWZ5LXN0YXJ0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTdG9yZUljb24gY2xhc3NOYW1lPVwidy00IGgtNCBtci0xIGZsZXgtc2hyaW5rLTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZVwiPuW6l+mTujwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkU3RvcmUgIT09ICdhbGwnICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0xIHRleHQteHMgYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCByb3VuZGVkLWZ1bGwgcHgtMS41IHB5LTAuNSBmbGV4LXNocmluay0wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAxXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwidy00IGgtNCBtbC1hdXRvIGZsZXgtc2hyaW5rLTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRTdG9yZSgnYWxsJyl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICDlhajpg6jlupfpk7pcclxuICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtwbGF0Zm9ybVN0b3Jlcy5tYXAoKHN0b3JlKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtzdG9yZS5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZFN0b3JlKHN0b3JlLmlkLnRvU3RyaW5nKCkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3N0b3JlLnN0b3JlX25hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51Q29udGVudD5cclxuICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnU+XHJcblxyXG4gICAgICAgICAgICAgICAgICB7Lyog54q25oCB5qCH562+5oyJ6ZKuICovfVxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgey8qIOWFqOmDqOeKtuaAgeaMiemSriAqL31cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17c2VsZWN0ZWRTdGF0dXMgPT09ICcnID8gXCJkZWZhdWx0XCIgOiBcIm91dGxpbmVcIn1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkU3RhdHVzKCcnKX1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAg5YWo6YOoXHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiDnirbmgIHmoIfnrb7mjInpkq4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAge3N0YXR1c09wdGlvbnMubWFwKChvcHRpb24pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZFN0YXR1cyA9PT0gb3B0aW9uLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtvcHRpb24udmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtpc1NlbGVjdGVkID8gXCJkZWZhdWx0XCIgOiBcIm91dGxpbmVcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTdGF0dXNTZWxlY3Qob3B0aW9uLnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLThcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbi5pY29uIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge29wdGlvbi5sYWJlbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIOe/u+ivkeeKtuaAgeagh+etvuaMiemSriAqL31cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiDnv7vor5HnirbmgIHmoIfnrb7mjInpkq4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAge3RyYW5zbGF0aW9uU3RhdHVzT3B0aW9ucy5tYXAoKG9wdGlvbikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNTZWxlY3RlZCA9IHNlbGVjdGVkVHJhbnNsYXRpb25TdGF0dXMgPT09IG9wdGlvbi52YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17b3B0aW9uLnZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17aXNTZWxlY3RlZCA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVHJhbnNsYXRpb25TdGF0dXNTZWxlY3Qob3B0aW9uLnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLThcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbi5pY29uIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAg57+76K+RLXtvcHRpb24ubGFiZWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiDliJvlu7rml7bpl7TnrZvpgIkgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnU+XHJcbiAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ3LTMyIGp1c3RpZnktc3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMSBmbGV4LXNocmluay0wXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHJ1bmNhdGVcIj7liJvlu7rml7bpl7Q8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsoZGF0ZVJhbmdlLnN0YXJ0IHx8IGRhdGVSYW5nZS5lbmQpICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0xIHRleHQteHMgYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCByb3VuZGVkLWZ1bGwgcHgtMS41IHB5LTAuNSBmbGV4LXNocmluay0wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAxXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwidy00IGgtNCBtbC1hdXRvIGZsZXgtc2hyaW5rLTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50IGNsYXNzTmFtZT1cInctNjQgcC0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+5byA5aeL5pel5pyfPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5bm0L+aciC/ml6VcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1hdERhdGVEaXNwbGF5KGRhdGVSYW5nZS5zdGFydCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZURhdGVJbnB1dChlLnRhcmdldC52YWx1ZSwgJ3N0YXJ0Jyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1heExlbmd0aD17MTB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj7nu5PmnZ/ml6XmnJ88L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLlubQv5pyIL+aXpVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybWF0RGF0ZURpc3BsYXkoZGF0ZVJhbmdlLmVuZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZURhdGVJbnB1dChlLnRhcmdldC52YWx1ZSwgJ2VuZCcpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXhMZW5ndGg9ezEwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudT5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiDlj7PkvqfvvJrmkJzntKLljLrln58gKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5pCc57SiU0tV44CBRUFO44CB5qCH6aKYLi4uXCJcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hWYWx1ZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy02NFwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIOaQnOe0olxyXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwic21cIiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e2hhbmRsZVJlc2V0fT5cclxuICAgICAgICAgICAgICAgICAgICA8Um90YXRlQ2N3IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAg6YeN572uXHJcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAge3VwbG9hZFByb2R1Y3RzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1jZW50ZXIgcC04XCI+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMlwiPuaaguaXoOS6p+WTgTwvaDM+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtc20gYm9yZGVyLXNlcGFyYXRlIGJvcmRlci1zcGFjaW5nLTAgdGFibGUtZml4ZWRcIj5cclxuICAgICAgICAgICAgICAgICAgPGNvbGdyb3VwPlxyXG4gICAgICAgICAgICAgICAgICAgIDxjb2wgY2xhc3NOYW1lPVwidy0xMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGNvbCBjbGFzc05hbWU9XCJ3LTE2XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8Y29sIGNsYXNzTmFtZT1cInctWzM1JV1cIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxjb2wgY2xhc3NOYW1lPVwidy0yMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGNvbCBjbGFzc05hbWU9XCJ3LVsxMCVdXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8Y29sIGNsYXNzTmFtZT1cInctMjRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxjb2wgY2xhc3NOYW1lPVwidy0yOFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGNvbCBjbGFzc05hbWU9XCJ3LTI0XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8Y29sIGNsYXNzTmFtZT1cInctMzJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxjb2wgY2xhc3NOYW1lPVwidy0zMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGNvbCBjbGFzc05hbWU9XCJ3LTE2XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9jb2xncm91cD5cclxuICAgICAgICAgICAgICAgICAgPHRoZWFkPlxyXG4gICAgICAgICAgICAgICAgICAgIDx0ciBjbGFzc05hbWU9XCJiZy1tdXRlZC8zMCBib3JkZXItYiBoLTE0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicC0zIHRleHQtbGVmdCBib3JkZXItciBib3JkZXItYm9yZGVyLzUwIGgtMTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3NlbGVjdGVkUHJvZHVjdHMubGVuZ3RoID09PSB1cGxvYWRQcm9kdWN0cy5sZW5ndGggJiYgdXBsb2FkUHJvZHVjdHMubGVuZ3RoID4gMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17aGFuZGxlU2VsZWN0QWxsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJwLTMgdGV4dC1sZWZ0IGZvbnQtbWVkaXVtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBib3JkZXItciBib3JkZXItYm9yZGVyLzUwIHRleHQtY2VudGVyIGgtMTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj7lm77niYc8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicC0zIHRleHQtbGVmdCBmb250LW1lZGl1bSB0ZXh0LW11dGVkLWZvcmVncm91bmQgYm9yZGVyLXIgYm9yZGVyLWJvcmRlci81MCB0ZXh0LWNlbnRlciBoLTE0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+5qCH6aKYL09mZmVySUQv5bqX6ZO6L+WIhuexuzwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJwLTMgdGV4dC1sZWZ0IGZvbnQtbWVkaXVtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBib3JkZXItciBib3JkZXItYm9yZGVyLzUwIHRleHQtbGVmdCBoLTE0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+54q25oCBPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInAtMyB0ZXh0LWxlZnQgZm9udC1tZWRpdW0gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTAgdGV4dC1jZW50ZXIgaC0xNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlNLVS9FQU48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicC0zIHRleHQtbGVmdCBmb250LW1lZGl1bSB0ZXh0LW11dGVkLWZvcmVncm91bmQgYm9yZGVyLXIgYm9yZGVyLWJvcmRlci81MCB0ZXh0LWNlbnRlciBoLTE0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+5bqT5a2YPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInAtMyB0ZXh0LWxlZnQgZm9udC1tZWRpdW0gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTAgdGV4dC1jZW50ZXIgaC0xNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPuWUruS7t++8iOKCrO+8iTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJwLTMgdGV4dC1sZWZ0IGZvbnQtbWVkaXVtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBib3JkZXItciBib3JkZXItYm9yZGVyLzUwIHRleHQtY2VudGVyIGgtMTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj7lpJror63oqIDmoIfpopg8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicC0zIHRleHQtbGVmdCBmb250LW1lZGl1bSB0ZXh0LW11dGVkLWZvcmVncm91bmQgYm9yZGVyLXIgYm9yZGVyLWJvcmRlci81MCB0ZXh0LWNlbnRlciBoLTE0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+5aSa6K+t6KiA5o+P6L+wPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInAtMyB0ZXh0LWxlZnQgZm9udC1tZWRpdW0gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTAgdGV4dC1jZW50ZXIgaC0xNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPuWIm+W7uuaXtumXtC/lj5HluIPml7bpl7Q8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicC0zIHRleHQtbGVmdCBmb250LW1lZGl1bSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaC0xNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPuaTjeS9nDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICAgICAgICA8L3RyPlxyXG4gICAgICAgICAgICAgICAgICA8L3RoZWFkPlxyXG4gICAgICAgICAgICAgICAgICA8dGJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAge3VwbG9hZFByb2R1Y3RzLm1hcCgocHJvZHVjdDogVXBsb2FkUHJvZHVjdExpc3RpbmcsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8dHJcclxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtgJHtwcm9kdWN0LmlkfS0ke3JlZnJlc2hLZXl9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYm9yZGVyLWIgYm9yZGVyLWJvcmRlci81MCBob3ZlcjpiZy1tdXRlZC8zMCB0cmFuc2l0aW9uLWNvbG9ycyBjdXJzb3ItcG9pbnRlciBoLTE2ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRQcm9kdWN0cy5pbmNsdWRlcyhwcm9kdWN0LmlkKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGluZGV4ICUgMiA9PT0gMCA/ICdiZy1iYWNrZ3JvdW5kJyA6ICdiZy1tdXRlZC8xMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiBoYW5kbGVQcm9kdWN0Q2xpY2socHJvZHVjdC5pZCwgZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJwLTMgYm9yZGVyLXIgYm9yZGVyLWJvcmRlci81MFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja2JveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2VsZWN0ZWRQcm9kdWN0cy5pbmNsdWRlcyhwcm9kdWN0LmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQpID0+IGhhbmRsZVNlbGVjdFByb2R1Y3QocHJvZHVjdC5pZCwgY2hlY2tlZCBhcyBib29sZWFuKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicC0zIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1tdXRlZCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG92ZXJmbG93LWhpZGRlbiBib3JkZXIgc2hhZG93LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5pbWFnZTEgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3Byb2R1Y3QuaW1hZ2UxfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17cHJvZHVjdC5lbmdsaXNoX3RpdGxlIHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHRleHQteHNcIj7ml6Dlm77niYc8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJwLTMgYm9yZGVyLXIgYm9yZGVyLWJvcmRlci81MFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZm9yZWdyb3VuZCBsZWFkaW5nLXRpZ2h0IGxpbmUtY2xhbXAtMVwiIHRpdGxlPXtwcm9kdWN0LmVuZ2xpc2hfdGl0bGUgfHwgJ+acquiuvue9ruagh+mimCd9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5lbmdsaXNoX3RpdGxlIHx8ICfmnKrorr7nva7moIfpopgnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGxpbmUtY2xhbXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBJRDoge3Byb2R1Y3QuaWR9IHwg5bqX6ZO6OiB7c3RvcmVzLmZpbmQocyA9PiBzLmlkID09PSBwcm9kdWN0LnN0b3JlX2lkKT8uc3RvcmVfbmFtZSB8fCAn5pyq55+l5bqX6ZO6J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC14cyBsaW5lLWNsYW1wLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5YiG57G7OiB7cHJvZHVjdC5wbGF0Zm9ybV9jYXRlZ29yeV9pZCB8fCAn5pyq6K6+572uJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicC0zIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzQmFkZ2UocHJvZHVjdC5zdGF0dXMpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicC0zIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZm9yZWdyb3VuZCBsZWFkaW5nLXRpZ2h0IGxpbmUtY2xhbXAtMVwiPntwcm9kdWN0LnVwc3RvcmVzX3NrdSB8fCBwcm9kdWN0LnNrdX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1mb3JlZ3JvdW5kIGxlYWRpbmctdGlnaHQgbGluZS1jbGFtcC0xXCI+e3Byb2R1Y3QudXBzdG9yZXNfZWFuIHx8IHByb2R1Y3QuZWFufTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicC0zIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTYwMCB0ZXh0LXNtXCI+e3Byb2R1Y3Quc3RvY2tfcXVhbnRpdHkgfHwgMH08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInAtMyBib3JkZXItciBib3JkZXItYm9yZGVyLzUwIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tNjAwIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LmRpc2NvdW50ZWRfcHJpY2UgPyBg4oKsJHtwcm9kdWN0LmRpc2NvdW50ZWRfcHJpY2V9YCA6ICfmnKrorr7nva4nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicC0zIGJvcmRlci1yIGJvcmRlci1ib3JkZXIvNTAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVuZGVyTXVsdGlMYW5ndWFnZVN0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb2R1Y3QubXVsdGlfdGl0bGVzIHx8IG51bGwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRQbGF0Zm9ybVJlcXVpcmVkTGFuZ3VhZ2VzKHBsYXRmb3JtKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJwLTMgYm9yZGVyLXIgYm9yZGVyLWJvcmRlci81MCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtyZW5kZXJNdWx0aUxhbmd1YWdlU3RhdHVzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdC5tdWx0aV9kZXNjcmlwdGlvbnMgfHwgbnVsbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldFBsYXRmb3JtUmVxdWlyZWRMYW5ndWFnZXMocGxhdGZvcm0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInAtMyBib3JkZXItciBib3JkZXItYm9yZGVyLzUwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKHByb2R1Y3QudXBsaXN0aW5nX2F0KS50b0xvY2FsZVN0cmluZygnemgtQ04nKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnU+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1vcmVIb3Jpem9udGFsIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51VHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50IGFsaWduPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtIG9uQ2xpY2s9eygpID0+IGhhbmRsZUVkaXRQcm9kdWN0KHByb2R1Y3QpfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOe8lui+keS6p+WTgVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb3B5IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5aSN5Yi25Yiw5YW25LuW5bqX6ZO6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVNlcGFyYXRvciAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5zdGF0dXMgPT09ICdkcmFmdCcgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFVwbG9hZCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5o+Q5Lqk5LiK5p62XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5zdGF0dXMgPT09ICdhY3RpdmUnICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOWQjOatpeeKtuaAgVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3QubGlzdGluZ3NfdHJhbnNsYXRpb25fc3RhdHVzID09PSAncGVuZGluZycgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbSBvbkNsaWNrPXsoKSA9PiBoYW5kbGVQcm9kdWN0VHJhbnNsYXRpb24ocHJvZHVjdCwgJ3RpdGxlJyl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFuZ3VhZ2VzIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOe/u+ivkeagh+mimFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gb25DbGljaz17KCkgPT4gaGFuZGxlUHJvZHVjdFRyYW5zbGF0aW9uKHByb2R1Y3QsICdkZXNjcmlwdGlvbicpfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOe/u+ivkeaPj+i/sFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXh0ZXJuYWxMaW5rIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5p+l55yL5Y6f5Lqn5ZOBXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVNlcGFyYXRvciAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZVByb2R1Y3QocHJvZHVjdC5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDliKDpmaTliIrnmbtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgPC90Ym9keT5cclxuICAgICAgICAgICAgICAgIDwvdGFibGU+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgey8qIOWbuuWumuW6lemDqOWIhumhteWMuuWfnyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTYgcHktMiBib3JkZXItdCBiZy1iYWNrZ3JvdW5kLzk1IGJhY2tkcm9wLWJsdXItc20gbXQtYXV0b1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAg5pi+56S6IHsoKHBhZ2luYXRpb24ucGFnZSAtIDEpICogcGFnaW5hdGlvbi5saW1pdCkgKyAxfS17TWF0aC5taW4ocGFnaW5hdGlvbi5wYWdlICogcGFnaW5hdGlvbi5saW1pdCwgcGFnaW5hdGlvbi50b3RhbCl9IOadoe+8jOWFsSB7cGFnaW5hdGlvbi50b3RhbH0g5p2h6K6w5b2VXHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17cGFnaW5hdGlvbi5wYWdlIDw9IDF9XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBmZXRjaFVwbG9hZFByb2R1Y3RzKHsgcGFnZTogcGFnaW5hdGlvbi5wYWdlIC0gMSB9KX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCBweC0zIHRleHQteHNcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIOS4iuS4gOmhtVxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICB7Lyog6aG156CB5oyJ6ZKuICovfVxyXG4gICAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiBNYXRoLm1pbig1LCBNYXRoLmNlaWwocGFnaW5hdGlvbi50b3RhbCAvIHBhZ2luYXRpb24ubGltaXQpKSB9LCAoXywgaSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgcGFnZU51bSA9IE1hdGgubWF4KDEsIHBhZ2luYXRpb24ucGFnZSAtIDIpICsgaTtcclxuICAgICAgICAgICAgICAgIGlmIChwYWdlTnVtID4gTWF0aC5jZWlsKHBhZ2luYXRpb24udG90YWwgLyBwYWdpbmF0aW9uLmxpbWl0KSkgcmV0dXJuIG51bGw7XHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIGtleT17cGFnZU51bX1cclxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e3BhZ2VOdW0gPT09IHBhZ2luYXRpb24ucGFnZSA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZmV0Y2hVcGxvYWRQcm9kdWN0cyh7IHBhZ2U6IHBhZ2VOdW0gfSl9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTAgdGV4dC14c1wiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7cGFnZU51bX1cclxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgIH0pfVxyXG5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3BhZ2luYXRpb24ucGFnZSA+PSBNYXRoLmNlaWwocGFnaW5hdGlvbi50b3RhbCAvIHBhZ2luYXRpb24ubGltaXQpfVxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZmV0Y2hVcGxvYWRQcm9kdWN0cyh7IHBhZ2U6IHBhZ2luYXRpb24ucGFnZSArIDEgfSl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggcHgtMyB0ZXh0LXhzXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICDkuIvkuIDpobVcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICB7Lyog5Lqn5ZOB5LiK5p626KGo5Y2VICovfVxyXG4gICAgICA8VXBsb2FkUHJvZHVjdEZvcm1cclxuICAgICAgICBvcGVuPXtzaG93Rm9ybX1cclxuICAgICAgICBvbkNsb3NlPXtoYW5kbGVDbG9zZUZvcm19XHJcbiAgICAgICAgb25TdWJtaXQ9e2hhbmRsZUZvcm1TdWJtaXR9XHJcbiAgICAgICAgcGxhdGZvcm09e3BsYXRmb3JtfVxyXG4gICAgICAgIHN0b3Jlcz17c3RvcmVzfVxyXG4gICAgICAgIGVkaXRpbmdQcm9kdWN0PXtlZGl0aW5nUHJvZHVjdH1cclxuICAgICAgICBtb2RlPXtlZGl0aW5nUHJvZHVjdCA/ICdlZGl0JyA6ICdhZGQnfVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgey8qIOWNleS4quS6p+WTgee/u+ivkeaooeaAgeahhiAqL31cclxuICAgICAge3RyYW5zbGF0aW9uUHJvZHVjdCAmJiAoXHJcbiAgICAgICAgPFRyYW5zbGF0aW9uTW9kYWxcclxuICAgICAgICAgIG9wZW49e3RyYW5zbGF0aW9uTW9kYWxPcGVufVxyXG4gICAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRUcmFuc2xhdGlvbk1vZGFsT3Blbn1cclxuICAgICAgICAgIHRpdGxlPXtg57+76K+RJHt0cmFuc2xhdGlvbkNvbnRlbnRUeXBlID09PSAndGl0bGUnID8gJ+agh+mimCcgOiAn5o+P6L+wJ30gLSAke3RyYW5zbGF0aW9uUHJvZHVjdC5za3V9YH1cclxuICAgICAgICAgIGluaXRpYWxUZXh0PXtcclxuICAgICAgICAgICAgdHJhbnNsYXRpb25Db250ZW50VHlwZSA9PT0gJ3RpdGxlJ1xyXG4gICAgICAgICAgICAgID8gdHJhbnNsYXRpb25Qcm9kdWN0LmVuZ2xpc2hfdGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICA6IHRyYW5zbGF0aW9uUHJvZHVjdC5tdWx0aV9kZXNjcmlwdGlvbnM/LkVOIHx8ICcnXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBzb3VyY2VMYW5nPVwiZW5cIlxyXG4gICAgICAgICAgdGFyZ2V0TGFuZ3M9e2dldFBsYXRmb3JtVGFyZ2V0TGFuZ3VhZ2VzKHBsYXRmb3JtKX1cclxuICAgICAgICAgIGNvbnRlbnRUeXBlPXt0cmFuc2xhdGlvbkNvbnRlbnRUeXBlfVxyXG4gICAgICAgICAgcGxhdGZvcm09e3BsYXRmb3JtfSAvLyDkvKDlhaXlubPlj7Dlj4LmlbBcclxuICAgICAgICAgIHNvdXJjZT1cImZvcm1fYmF0Y2hcIiAvLyDmibnph4/nv7vor5HlnLrmma9cclxuICAgICAgICAgIG9uVHJhbnNsYXRpb25Db21wbGV0ZT17aGFuZGxlVHJhbnNsYXRpb25Db21wbGV0ZX1cclxuICAgICAgICAgIG9uVHJhbnNsYXRpb25FcnJvcj17KGVycm9ycykgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdUcmFuc2xhdGlvbiBlcnJvcnM6JywgZXJyb3JzKVxyXG4gICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgdGl0bGU6ICfnv7vor5HlpLHotKUnLFxyXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAn6YOo5YiG6K+t6KiA57+76K+R5aSx6LSl77yM6K+35p+l55yL6K+m5oOFJyxcclxuICAgICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7Lyog5om56YeP57+76K+R6L+b5bqm5a+56K+d5qGGICovfVxyXG4gICAgICA8QmF0Y2hUcmFuc2xhdGlvblByb2dyZXNzXHJcbiAgICAgICAgb3Blbj17YmF0Y2hUcmFuc2xhdGlvblByb2dyZXNzLmlzT3Blbn1cclxuICAgICAgICBvbk9wZW5DaGFuZ2U9e2JhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcy5zZXRJc09wZW59XHJcbiAgICAgICAgaXRlbXM9e2JhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcy5pdGVtc31cclxuICAgICAgICBpc1Byb2Nlc3Npbmc9e2JhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcy5pc1Byb2Nlc3Npbmd9XHJcbiAgICAgICAgb25DYW5jZWw9e2JhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcy5jYW5jZWxUcmFuc2xhdGlvbn1cclxuICAgICAgICBvbkNvbXBsZXRlPXthc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAvLyDnv7vor5HlrozmiJDlkI7liLfmlrDmlbDmja5cclxuICAgICAgICAgIGF3YWl0IGZldGNoVXBsb2FkUHJvZHVjdHMoeyBfdDogRGF0ZS5ub3coKSB9KVxyXG4gICAgICAgICAgc2V0UmVmcmVzaEtleShwcmV2ID0+IHByZXYgKyAxKVxyXG4gICAgICAgICAgLy8g5bu26L+f5YWz6Zet5a+56K+d5qGG77yM6K6p55So5oi355yL5Yiw5a6M5oiQ54q25oCBXHJcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgYmF0Y2hUcmFuc2xhdGlvblByb2dyZXNzLmNsb3NlRGlhbG9nKClcclxuICAgICAgICAgIH0sIDIwMDApXHJcbiAgICAgICAgfX1cclxuICAgICAgLz5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VVcGxvYWRQcm9kdWN0cyIsInVzZVN0b3JlcyIsInVzZURyb3BzaGlwUHJvZHVjdHMiLCJ1c2VDb25maXJtIiwidXNlVG9hc3QiLCJVcGxvYWRQcm9kdWN0Rm9ybSIsIlRyYW5zbGF0aW9uTW9kYWwiLCJCYXRjaFRyYW5zbGF0aW9uUHJvZ3Jlc3MiLCJ1c2VCYXRjaFRyYW5zbGF0aW9uUHJvZ3Jlc3MiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJCdXR0b24iLCJJbnB1dCIsIkNoZWNrYm94IiwiRHJvcGRvd25NZW51IiwiRHJvcGRvd25NZW51Q29udGVudCIsIkRyb3Bkb3duTWVudUl0ZW0iLCJEcm9wZG93bk1lbnVUcmlnZ2VyIiwiRHJvcGRvd25NZW51U2VwYXJhdG9yIiwiUGx1cyIsIlVwbG9hZCIsIkVkaXQiLCJDb3B5IiwiUmVmcmVzaEN3IiwiQ2hldnJvbkRvd24iLCJUcmFzaDIiLCJTZWFyY2giLCJSb3RhdGVDY3ciLCJNb3JlSG9yaXpvbnRhbCIsIkxhbmd1YWdlcyIsIkZpbGVUZXh0IiwiRG93bmxvYWQiLCJDYWxlbmRhciIsIlN0b3JlIiwiU3RvcmVJY29uIiwiQ2hlY2tDaXJjbGUiLCJDbG9jayIsIkFsZXJ0Q2lyY2xlIiwiRXh0ZXJuYWxMaW5rIiwic3RhdHVzT3B0aW9ucyIsInZhbHVlIiwibGFiZWwiLCJpY29uIiwidHJhbnNsYXRpb25TdGF0dXNPcHRpb25zIiwiVXBsb2FkUHJvZHVjdFBhZ2VGdWxsIiwicGxhdGZvcm0iLCJ0cmFuc2xhdGlvblByb2R1Y3QiLCJzZWxlY3RlZFByb2R1Y3RzIiwic2V0U2VsZWN0ZWRQcm9kdWN0cyIsInNlYXJjaFZhbHVlIiwic2V0U2VhcmNoVmFsdWUiLCJzZWxlY3RlZFN0b3JlIiwic2V0U2VsZWN0ZWRTdG9yZSIsInNob3dGb3JtIiwic2V0U2hvd0Zvcm0iLCJlZGl0aW5nUHJvZHVjdCIsInNldEVkaXRpbmdQcm9kdWN0IiwiY29uZmlybSIsInRvYXN0Iiwic2VsZWN0ZWRTdGF0dXMiLCJzZXRTZWxlY3RlZFN0YXR1cyIsInNlbGVjdGVkVHJhbnNsYXRpb25TdGF0dXMiLCJzZXRTZWxlY3RlZFRyYW5zbGF0aW9uU3RhdHVzIiwiZGF0ZVJhbmdlIiwic2V0RGF0ZVJhbmdlIiwic3RhcnQiLCJlbmQiLCJ0cmFuc2xhdGlvbk1vZGFsT3BlbiIsInNldFRyYW5zbGF0aW9uTW9kYWxPcGVuIiwic2V0VHJhbnNsYXRpb25Qcm9kdWN0IiwidHJhbnNsYXRpb25Db250ZW50VHlwZSIsInNldFRyYW5zbGF0aW9uQ29udGVudFR5cGUiLCJyZWZyZXNoS2V5Iiwic2V0UmVmcmVzaEtleSIsImJhdGNoVHJhbnNsYXRpb25Qcm9ncmVzcyIsInVwbG9hZFByb2R1Y3RzIiwibG9hZGluZyIsInByb2R1Y3RzTG9hZGluZyIsImVycm9yIiwicHJvZHVjdHNFcnJvciIsInBhZ2luYXRpb24iLCJmZXRjaFVwbG9hZFByb2R1Y3RzIiwiY3JlYXRlVXBsb2FkUHJvZHVjdCIsInVwZGF0ZVVwbG9hZFByb2R1Y3QiLCJkZWxldGVVcGxvYWRQcm9kdWN0Iiwic3RvcmVzIiwic3RvcmVzTG9hZGluZyIsImZldGNoU3RvcmVzIiwiZHJvcHNoaXBQcm9kdWN0cyIsImRyb3BzaGlwTG9hZGluZyIsImZldGNoRHJvcHNoaXBQcm9kdWN0cyIsInBsYXRmb3JtU3RvcmVzIiwiZmlsdGVyIiwic3RvcmUiLCJwbGF0Zm9ybV9jb2RlIiwiaGFuZGxlU3RhdHVzU2VsZWN0Iiwic3RhdHVzIiwicHJldiIsImhhbmRsZVRyYW5zbGF0aW9uU3RhdHVzU2VsZWN0IiwiZm9ybWF0RGF0ZURpc3BsYXkiLCJkYXRlU3RyIiwiaW5jbHVkZXMiLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJzcGxpdCIsInBhZFN0YXJ0IiwiaGFuZGxlRGF0ZUlucHV0IiwidHlwZSIsIm51bWVyaWNWYWx1ZSIsInJlcGxhY2UiLCJsZW5ndGgiLCJmb3JtYXR0ZWRWYWx1ZSIsImRpc3BsYXlWYWx1ZSIsInNsaWNlIiwiZGF0ZSIsIkRhdGUiLCJwYXJzZUludCIsImdldEZ1bGxZZWFyIiwiZ2V0TW9udGgiLCJnZXREYXRlIiwiaGFuZGxlUmVzZXQiLCJoYW5kbGVBcHBseUZpbHRlcnMiLCJwYXJhbXMiLCJzZWFyY2giLCJzdG9yZV9pZCIsInRyYW5zbGF0aW9uX3N0YXR1cyIsInN0YXJ0X2RhdGUiLCJlbmRfZGF0ZSIsInRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImhhbmRsZUNyZWF0ZVByb2R1Y3QiLCJkYXRhIiwiaGFuZGxlVXBkYXRlUHJvZHVjdCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiaWQiLCJjb25zb2xlIiwiaGFuZGxlRm9ybVN1Ym1pdCIsImhhbmRsZUVkaXRQcm9kdWN0IiwicHJvZHVjdCIsImhhbmRsZUNsb3NlRm9ybSIsImhhbmRsZURlbGV0ZVByb2R1Y3QiLCJwcm9kdWN0SWQiLCJjb25maXJtZWQiLCJjb25maXJtVGV4dCIsImNhbmNlbFRleHQiLCJoYW5kbGVCYXRjaERlbGV0ZSIsIlByb21pc2UiLCJhbGwiLCJtYXAiLCJoYW5kbGVCYXRjaFRyYW5zbGF0aW9uIiwic2VsZWN0ZWRQcm9kdWN0c0RhdGEiLCJwIiwicHJvZ3Jlc3NJdGVtcyIsInNrdSIsIm5hbWUiLCJlbmdsaXNoX3RpdGxlIiwic3RhcnRUcmFuc2xhdGlvbiIsInJlc3VsdCIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkVycm9yIiwiZm9yRWFjaCIsImluZGV4Iiwic2V0UHJvY2Vzc2luZ0l0ZW0iLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwic291cmNlIiwic291cmNlTGFuZyIsInRhcmdldExhbmdzIiwiZ2V0UGxhdGZvcm1UYXJnZXRMYW5ndWFnZXMiLCJwcm9kdWN0aWRzIiwiam9pbiIsIm9rIiwianNvbiIsImNvZGUiLCJzdWNjZXNzIiwicmVzdWx0cyIsInN1Y2Nlc3NDb3VudCIsInN1Y2Nlc3NmdWwiLCJmYWlsZWRDb3VudCIsImZhaWxlZCIsImRldGFpbHMiLCJkZXRhaWwiLCJzZXRTdWNjZXNzSXRlbSIsInNldEVycm9ySXRlbSIsImZpbmlzaFRyYW5zbGF0aW9uIiwibG9nIiwiX3QiLCJub3ciLCJmb3JjZV9yZWZyZXNoIiwibXVsdGlfdGl0bGVzIiwibXVsdGlfZGVzY3JpcHRpb25zIiwibWVzc2FnZSIsImdldFN0YXR1c0JhZGdlIiwic3RhdHVzTWFwIiwiZHJhZnQiLCJjbGFzc05hbWUiLCJwZW5kaW5nIiwiYWN0aXZlIiwiaW5hY3RpdmUiLCJjb25maWciLCJzcGFuIiwiZ2V0VHJhbnNsYXRpb25TdGF0dXNCYWRnZSIsImNvbXBsZXRlZCIsImdldFBsYXRmb3JtTmFtZSIsInBsYXRmb3JtQ29kZSIsInBsYXRmb3JtTWFwIiwid29ydGVuIiwicGhoIiwiYW1hem9uIiwiZWJheSIsInNob3BpZnkiLCJ0b1VwcGVyQ2FzZSIsImdldFBsYXRmb3JtUmVxdWlyZWRMYW5ndWFnZXMiLCJwbGF0Zm9ybUxhbmd1YWdlcyIsImxhbmd1YWdlTWFwIiwibGFuZyIsImhhbmRsZVByb2R1Y3RUcmFuc2xhdGlvbiIsImNvbnRlbnRUeXBlIiwiaGFuZGxlVHJhbnNsYXRpb25Db21wbGV0ZSIsInRyYW5zbGF0aW9ucyIsInVwZGF0ZURhdGEiLCJsaXN0aW5nc190cmFuc2xhdGlvbl9zdGF0dXMiLCJyZW5kZXJNdWx0aUxhbmd1YWdlU3RhdHVzIiwibXVsdGlMYW5nRGF0YSIsInJlcXVpcmVkTGFuZ3VhZ2VzIiwiZGl2IiwiaGFzVHJhbnNsYXRpb24iLCJ0cmltIiwiaGFuZGxlU2VsZWN0QWxsIiwiY2hlY2tlZCIsImhhbmRsZVNlbGVjdFByb2R1Y3QiLCJoYW5kbGVQcm9kdWN0Q2xpY2siLCJldmVudCIsInRhcmdldCIsImNsb3Nlc3QiLCJpc1NlbGVjdGVkIiwib25DbGljayIsInBsYXRmb3JtTmFtZSIsInNpemUiLCJhc0NoaWxkIiwiYWxpZ24iLCJ0b1N0cmluZyIsInN0b3JlX25hbWUiLCJvcHRpb24iLCJwbGFjZWhvbGRlciIsIm9uQ2hhbmdlIiwiZSIsIm1heExlbmd0aCIsImgzIiwidGFibGUiLCJjb2xncm91cCIsImNvbCIsInRoZWFkIiwidHIiLCJ0aCIsIm9uQ2hlY2tlZENoYW5nZSIsInRib2R5IiwidGQiLCJpbWFnZTEiLCJpbWciLCJzcmMiLCJhbHQiLCJmaW5kIiwicyIsInBsYXRmb3JtX2NhdGVnb3J5X2lkIiwidXBzdG9yZXNfc2t1IiwidXBzdG9yZXNfZWFuIiwiZWFuIiwic3RvY2tfcXVhbnRpdHkiLCJkaXNjb3VudGVkX3ByaWNlIiwidXBsaXN0aW5nX2F0IiwidG9Mb2NhbGVTdHJpbmciLCJwYWdlIiwibGltaXQiLCJNYXRoIiwibWluIiwidG90YWwiLCJkaXNhYmxlZCIsIkFycmF5IiwiZnJvbSIsImNlaWwiLCJfIiwiaSIsInBhZ2VOdW0iLCJtYXgiLCJvcGVuIiwib25DbG9zZSIsIm9uU3VibWl0IiwibW9kZSIsIm9uT3BlbkNoYW5nZSIsImluaXRpYWxUZXh0IiwiRU4iLCJvblRyYW5zbGF0aW9uQ29tcGxldGUiLCJvblRyYW5zbGF0aW9uRXJyb3IiLCJlcnJvcnMiLCJpc09wZW4iLCJzZXRJc09wZW4iLCJpdGVtcyIsImlzUHJvY2Vzc2luZyIsIm9uQ2FuY2VsIiwiY2FuY2VsVHJhbnNsYXRpb24iLCJvbkNvbXBsZXRlIiwiY2xvc2VEaWFsb2ciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});