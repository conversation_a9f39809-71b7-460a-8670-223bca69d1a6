"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态 - 产品状态改为单选\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 单选状态\n    ;\n    const [selectedTranslationStatus, setSelectedTranslationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 翻译状态也改为单选\n    ;\n    const [dateDropdownOpen, setDateDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 控制日期下拉菜单开关\n    ;\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数 - 改为单选模式\n    const handleStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleTranslationStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedTranslationStatus((prev)=>prev === status ? \"\" : status);\n    };\n    // 日期格式化函数\n    const formatDateDisplay = (dateStr)=>{\n        if (!dateStr) return \"\";\n        // 如果是YYYY-MM-DD格式，转换为YYYY年MM月DD日显示\n        if (dateStr.includes(\"-\")) {\n            const [year, month, day] = dateStr.split(\"-\");\n            if (year && month && day) {\n                return \"\".concat(year, \"年\").concat(month.padStart(2, \"0\"), \"月\").concat(day.padStart(2, \"0\"), \"日\");\n            }\n        }\n        return dateStr;\n    };\n    // 处理日期输入\n    const handleDateInput = (value, type)=>{\n        // 只允许数字输入\n        const numericValue = value.replace(/\\D/g, \"\");\n        if (numericValue.length <= 8) {\n            let formattedValue = numericValue;\n            let displayValue = numericValue;\n            // 自动格式化显示\n            if (numericValue.length >= 4) {\n                const year = numericValue.slice(0, 4);\n                displayValue = \"\".concat(year, \"年\");\n                if (numericValue.length >= 6) {\n                    const month = numericValue.slice(4, 6);\n                    displayValue += \"\".concat(month, \"月\");\n                    if (numericValue.length === 8) {\n                        const day = numericValue.slice(6, 8);\n                        displayValue += \"\".concat(day, \"日\");\n                        // 转换为YYYY-MM-DD格式存储\n                        formattedValue = \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n                    }\n                }\n            }\n            // 如果输入完整的8位数字，转换为标准日期格式\n            if (numericValue.length === 8) {\n                const year = numericValue.slice(0, 4);\n                const month = numericValue.slice(4, 6);\n                const day = numericValue.slice(6, 8);\n                // 验证日期有效性\n                const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\n                if (date.getFullYear() == parseInt(year) && date.getMonth() == parseInt(month) - 1 && date.getDate() == parseInt(day)) {\n                    formattedValue = \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n                }\n            }\n            setDateRange((prev)=>({\n                    ...prev,\n                    [type]: numericValue.length === 8 ? formattedValue : numericValue\n                }));\n        }\n    };\n    const handleReset = ()=>{\n        setSelectedStatus(\"\");\n        setSelectedTranslationStatus(\"\");\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatus) {\n            params.status = selectedStatus;\n        }\n        if (selectedTranslationStatus) {\n            params.translation_status = selectedTranslationStatus;\n        }\n        // 只有完整的日期格式（YYYY-MM-DD）才应用筛选\n        if (dateRange.start && dateRange.start.includes(\"-\") && dateRange.start.length === 10) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end && dateRange.end.includes(\"-\") && dateRange.end.length === 10) {\n            params.end_date = dateRange.end;\n        }\n        console.log(\"应用筛选参数:\", params) // 调试日志\n        ;\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatus,\n        selectedTranslationStatus,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 539,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 552,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 659,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 711,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 710,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 723,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 722,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 803,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: selectedStatus === \"\" ? \"default\" : \"outline\",\n                                                                onClick: ()=>setSelectedStatus(\"\"),\n                                                                className: \"h-8\",\n                                                                children: \"全部\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            statusOptions.map((option)=>{\n                                                                const isSelected = selectedStatus === option.value;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: isSelected ? \"default\" : \"outline\",\n                                                                    onClick: ()=>handleStatusSelect(option.value),\n                                                                    className: \"h-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        option.label\n                                                                    ]\n                                                                }, option.value, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: translationStatusOptions.map((option)=>{\n                                                            const isSelected = selectedTranslationStatus === option.value;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: isSelected ? \"default\" : \"outline\",\n                                                                onClick: ()=>handleTranslationStatusSelect(option.value),\n                                                                className: \"h-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"翻译-\",\n                                                                    option.label\n                                                                ]\n                                                            }, option.value, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 853,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        open: dateDropdownOpen,\n                                                        onOpenChange: setDateDropdownOpen,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 878,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-64 p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 884,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    placeholder: \"年/月/日\",\n                                                                                    value: formatDateDisplay(dateRange.start),\n                                                                                    onChange: (e)=>handleDateInput(e.target.value, \"start\"),\n                                                                                    className: \"mt-1\",\n                                                                                    maxLength: 10\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 885,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 883,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 894,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    placeholder: \"年/月/日\",\n                                                                                    value: formatDateDisplay(dateRange.end),\n                                                                                    onChange: (e)=>handleDateInput(e.target.value, \"end\"),\n                                                                                    className: \"mt-1\",\n                                                                                    maxLength: 10\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 895,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-end gap-2 pt-2 border-t\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    onClick: ()=>{\n                                                                                        setDateRange({\n                                                                                            start: \"\",\n                                                                                            end: \"\"\n                                                                                        });\n                                                                                    },\n                                                                                    children: \"清空\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 906,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        handleApplyFilters();\n                                                                                    },\n                                                                                    children: \"确定\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 915,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 931,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 951,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 962,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 965,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 966,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 967,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 990,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1002,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1024,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1038,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1044,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1047,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1050,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1055,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1060,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1061,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1065,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1093,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1098,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1097,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1102,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1101,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1105,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1108,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1107,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1114,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1113,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1121,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1120,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1125,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1124,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1131,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1134,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1139,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1135,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1096,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1158,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1174,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 738,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 737,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1201,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1213,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.BatchTranslationProgress, {\n                open: batchTranslationProgress.isOpen,\n                onOpenChange: batchTranslationProgress.setIsOpen,\n                items: batchTranslationProgress.items,\n                isProcessing: batchTranslationProgress.isProcessing,\n                onCancel: batchTranslationProgress.cancelTranslation,\n                onComplete: async ()=>{\n                    // 翻译完成后刷新数据\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    // 延迟关闭对话框，让用户看到完成状态\n                    setTimeout(()=>{\n                        batchTranslationProgress.closeDialog();\n                    }, 2000);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1240,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 736,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"3gWU682ZUzODZuVAQX397Bo2XEg=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});