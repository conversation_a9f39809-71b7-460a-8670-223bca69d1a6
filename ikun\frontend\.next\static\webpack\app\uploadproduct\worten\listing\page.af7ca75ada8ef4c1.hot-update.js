"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态 - 产品状态改为单选\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 单选状态\n    ;\n    const [selectedTranslationStatus, setSelectedTranslationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 翻译状态也改为单选\n    ;\n    const [dateDropdownOpen, setDateDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 控制日期下拉菜单开关\n    ;\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数 - 改为单选模式\n    const handleStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleTranslationStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedTranslationStatus((prev)=>prev === status ? \"\" : status);\n    };\n    // 日期格式化函数\n    const formatDateDisplay = (dateStr)=>{\n        if (!dateStr) return \"\";\n        // 如果是YYYY-MM-DD格式，转换为YYYY年MM月DD日显示\n        if (dateStr.includes(\"-\")) {\n            const [year, month, day] = dateStr.split(\"-\");\n            if (year && month && day) {\n                return \"\".concat(year, \"年\").concat(month.padStart(2, \"0\"), \"月\").concat(day.padStart(2, \"0\"), \"日\");\n            }\n        }\n        return dateStr;\n    };\n    // 处理日期输入\n    const handleDateInput = (value, type)=>{\n        // 只允许数字输入\n        const numericValue = value.replace(/\\D/g, \"\");\n        if (numericValue.length <= 8) {\n            let formattedValue = numericValue;\n            let displayValue = numericValue;\n            // 自动格式化显示\n            if (numericValue.length >= 4) {\n                const year = numericValue.slice(0, 4);\n                displayValue = \"\".concat(year, \"年\");\n                if (numericValue.length >= 6) {\n                    const month = numericValue.slice(4, 6);\n                    displayValue += \"\".concat(month, \"月\");\n                    if (numericValue.length === 8) {\n                        const day = numericValue.slice(6, 8);\n                        displayValue += \"\".concat(day, \"日\");\n                        // 转换为YYYY-MM-DD格式存储\n                        formattedValue = \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n                    }\n                }\n            }\n            // 如果输入完整的8位数字，转换为标准日期格式\n            if (numericValue.length === 8) {\n                const year = numericValue.slice(0, 4);\n                const month = numericValue.slice(4, 6);\n                const day = numericValue.slice(6, 8);\n                // 验证日期有效性\n                const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\n                if (date.getFullYear() == parseInt(year) && date.getMonth() == parseInt(month) - 1 && date.getDate() == parseInt(day)) {\n                    formattedValue = \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n                }\n            }\n            setDateRange((prev)=>({\n                    ...prev,\n                    [type]: numericValue.length === 8 ? formattedValue : numericValue\n                }));\n        }\n    };\n    const handleReset = ()=>{\n        setSelectedStatus(\"\");\n        setSelectedTranslationStatus(\"\");\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatus) {\n            params.status = selectedStatus;\n        }\n        if (selectedTranslationStatus) {\n            params.translation_status = selectedTranslationStatus;\n        }\n        // 只有完整的日期格式（YYYY-MM-DD）才应用筛选\n        if (dateRange.start && dateRange.start.includes(\"-\") && dateRange.start.length === 10) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end && dateRange.end.includes(\"-\") && dateRange.end.length === 10) {\n            params.end_date = dateRange.end;\n        }\n        console.log(\"应用筛选参数:\", params) // 调试日志\n        ;\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatus,\n        selectedTranslationStatus,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 539,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 552,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 659,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 711,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 710,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 723,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 722,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 803,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: selectedStatus === \"\" ? \"default\" : \"outline\",\n                                                                onClick: ()=>setSelectedStatus(\"\"),\n                                                                className: \"h-8\",\n                                                                children: \"全部\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            statusOptions.map((option)=>{\n                                                                const isSelected = selectedStatus === option.value;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: isSelected ? \"default\" : \"outline\",\n                                                                    onClick: ()=>handleStatusSelect(option.value),\n                                                                    className: \"h-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        option.label\n                                                                    ]\n                                                                }, option.value, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: translationStatusOptions.map((option)=>{\n                                                            const isSelected = selectedTranslationStatus === option.value;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: isSelected ? \"default\" : \"outline\",\n                                                                onClick: ()=>handleTranslationStatusSelect(option.value),\n                                                                className: \"h-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"翻译-\",\n                                                                    option.label\n                                                                ]\n                                                            }, option.value, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 853,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 878,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-64 p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 884,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    placeholder: \"年/月/日\",\n                                                                                    value: formatDateDisplay(dateRange.start),\n                                                                                    onChange: (e)=>handleDateInput(e.target.value, \"start\"),\n                                                                                    className: \"mt-1\",\n                                                                                    maxLength: 10\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 885,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 883,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 894,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    placeholder: \"年/月/日\",\n                                                                                    value: formatDateDisplay(dateRange.end),\n                                                                                    onChange: (e)=>handleDateInput(e.target.value, \"end\"),\n                                                                                    className: \"mt-1\",\n                                                                                    maxLength: 10\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 895,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-end gap-2 pt-2 border-t\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    onClick: ()=>{\n                                                                                        setDateRange({\n                                                                                            start: \"\",\n                                                                                            end: \"\"\n                                                                                        });\n                                                                                    },\n                                                                                    children: \"清空\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 906,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        handleApplyFilters();\n                                                                                    },\n                                                                                    children: \"确定\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 915,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 931,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 951,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 962,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 965,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 966,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 967,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 990,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1002,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1024,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 1038,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1044,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1047,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1050,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1055,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1060,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1061,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1065,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1093,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1098,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1097,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1102,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1101,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1105,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1108,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1107,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1114,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1113,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1121,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1120,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1125,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1124,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1131,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1134,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1139,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1135,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1096,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1158,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1174,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 738,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 737,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1201,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1213,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.BatchTranslationProgress, {\n                open: batchTranslationProgress.isOpen,\n                onOpenChange: batchTranslationProgress.setIsOpen,\n                items: batchTranslationProgress.items,\n                isProcessing: batchTranslationProgress.isProcessing,\n                onCancel: batchTranslationProgress.cancelTranslation,\n                onComplete: async ()=>{\n                    // 翻译完成后刷新数据\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    // 延迟关闭对话框，让用户看到完成状态\n                    setTimeout(()=>{\n                        batchTranslationProgress.closeDialog();\n                    }, 2000);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1240,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 736,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"3gWU682ZUzODZuVAQX397Bo2XEg=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});