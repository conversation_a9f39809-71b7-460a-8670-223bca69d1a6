/**
 * 翻译服务主类
 */

import { logger } from '@/utils/logger';
import { TranslationConfig } from './config';
import { MTranProvider } from './providers/mtranProvider';
import { OpenAICompatibleProvider } from './providers/openaiCompatibleProvider';
import { BaseTranslationProvider } from './providers/baseProvider';
import {
  ITranslationProvider,
  TranslationRequest,
  TranslationResponse,
  BatchTranslationRequest,
  BatchTranslationResponse,
  ProductTranslationRequest,
  ProductTranslationResponse,
  TranslationStats,
  LanguageCode
} from './types';

/**
 * 翻译服务管理器
 */
export class TranslationService {
  private static instance: TranslationService;
  private config: TranslationConfig;
  private providers: Map<string, ITranslationProvider | OpenAICompatibleProvider>;
  private stats: TranslationStats;

  private constructor() {
    this.config = TranslationConfig.getInstance();
    this.providers = new Map();
    this.stats = {
      totalRequests: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      lastRequestTime: new Date()
    };

    // 移除自动初始化，改为按需初始化
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  /**
   * 确保翻译提供商已初始化（按需初始化）
   */
  private ensureProvidersInitialized(): void {
    if (this.providers.size === 0) {
      this.initializeProviders();
    }
  }

  /**
   * 初始化翻译提供商
   * 注意：OpenAI兼容提供商不在这里初始化，而是在getProvider中动态创建
   */
  private initializeProviders(): void {
    const providerConfigs = this.config.getAllProviderConfigs();

    Object.entries(providerConfigs).forEach(([name, config]) => {
      try {
        let provider: ITranslationProvider;

        switch (name) {
          case 'mtran':
            provider = new MTranProvider(config);
            this.providers.set(name, provider);
            logger.info(`已初始化翻译提供商: ${name}`);
            break;
          case 'deepseek_huoshan':
            // DeepSeek火山引擎提供商 - 动态创建，不在此处初始化
            logger.info(`DeepSeek火山引擎提供商配置已加载，将根据subService动态创建实例`);
            break;
          case 'with_openai':
            // OpenAI兼容提供商 - 动态创建，不在此处初始化
            logger.info(`OpenAI兼容提供商配置已加载，将根据subService动态创建实例`);
            break;
          default:
            logger.warn(`Unknown provider type: ${name}`);
            break;
        }
      } catch (error) {
        logger.error(`初始化翻译提供商失败 ${name}:`, error);
      }
    });
  }

  /**
   * 获取提供商实例（智能选择）
   * 支持动态创建 OpenAI 兼容提供商
   */
  private getProvider(
    providerName?: string,
    contentType?: 'title' | 'description' | 'selling_point',
    sourceLang?: LanguageCode,
    targetLang?: LanguageCode,
    platform?: string,
    source?: string
  ): ITranslationProvider | OpenAICompatibleProvider {
    // 确保提供商已初始化
    this.ensureProvidersInitialized();

    let selectedProvider: string;
    let subService: string | undefined;
    let selectionMethod: string;

    logger.info('[TranslationService] 开始获取翻译提供商', {
      providerName,
      contentType,
      sourceLang,
      targetLang,
      platform,
      source,
      availableProviders: Array.from(this.providers.keys())
    });

    if (providerName) {
      // 如果明确指定了提供商，直接使用
      selectedProvider = providerName;
      selectionMethod = 'manual-specified';
      logger.info('[TranslationService] 使用手动指定的提供商', {
        selectedProvider,
        selectionMethod
      });
    } else if (contentType && sourceLang && targetLang) {
      // 使用智能选择逻辑
      try {
        const selection = this.config.selectProvider(contentType, sourceLang, targetLang, platform, source);
        selectedProvider = selection.provider;
        subService = selection.subService;
        selectionMethod = 'intelligent-selection';

        logger.info(`[TranslationService] 智能选择提供商完成`, {
          selectedProvider,
          subService,
          selectionMethod,
          contentType,
          sourceLang,
          targetLang,
          platform,
          source
        });
      } catch (configError) {
        logger.error('[TranslationService] 智能选择提供商失败', {
          contentType,
          sourceLang,
          targetLang,
          platform,
          source,
          error: configError instanceof Error ? configError.message : 'Unknown error'
        });
        // 直接抛出配置错误，不进行回退
        throw configError;
      }
    } else {
      // 使用默认提供商
      selectedProvider = this.config.getDefaultProvider();
      selectionMethod = 'default-fallback';
      logger.info('[TranslationService] 使用默认提供商（参数不足）', {
        selectedProvider,
        selectionMethod,
        reason: '缺少必要的选择参数'
      });
    }

    // 获取或创建提供商实例
    return this.getOrCreateProviderInstance(selectedProvider, subService, selectionMethod);
  }

  /**
   * 获取或创建提供商实例
   * 对于 OpenAI 兼容提供商，根据 subService 动态创建实例
   */
  private getOrCreateProviderInstance(
    providerName: string,
    subService?: string,
    selectionMethod?: string
  ): ITranslationProvider | OpenAICompatibleProvider {
    // 对于 MTran 提供商，直接从缓存获取
    if (providerName === 'mtran') {
      const provider = this.providers.get(providerName);
      if (!provider) {
        throw new Error(`MTran 提供商未初始化`);
      }
      return provider;
    }

    // 对于 OpenAI 兼容提供商，动态创建实例
    if (providerName === 'deepseek_huoshan' || providerName === 'with_openai') {
      if (!subService) {
        throw new Error(`${providerName} 提供商需要指定 subService`);
      }

      // 创建缓存键，包含提供商名称和子服务
      const cacheKey = `${providerName}:${subService}`;

      // 检查是否已有缓存的实例
      let provider = this.providers.get(cacheKey);
      if (provider) {
        logger.debug(`[TranslationService] 使用缓存的提供商实例: ${cacheKey}`);
        return provider;
      }

      // 动态创建新实例
      provider = this.createOpenAICompatibleProvider(providerName, subService);

      // 缓存实例
      this.providers.set(cacheKey, provider);

      logger.info(`[TranslationService] ✅ 成功创建并缓存提供商实例: ${cacheKey}`, {
        providerName,
        subService,
        selectionMethod,
        providerDisplayName: provider.name
      });

      return provider;
    }

    // 未知提供商类型
    throw new Error(`不支持的提供商类型: ${providerName}`);
  }

  /**
   * 创建 OpenAI 兼容提供商实例
   */
  private createOpenAICompatibleProvider(
    providerType: 'deepseek_huoshan' | 'with_openai',
    subService: string
  ): OpenAICompatibleProvider {
    try {
      // 获取提供商的数据库配置
      const dbConfig = this.config.getProviders()[providerType];
      if (!dbConfig || !dbConfig.enabled) {
        throw new Error(`提供商 ${providerType} 未启用或配置不存在`);
      }

      // 创建基础配置
      const baseConfig = this.config.getProviderConfig(providerType);
      if (!baseConfig) {
        throw new Error(`无法获取提供商 ${providerType} 的基础配置`);
      }

      // 创建 OpenAI 兼容提供商实例
      const provider = new OpenAICompatibleProvider(
        baseConfig,
        providerType,
        subService,
        dbConfig
      );

      logger.info(`[TranslationService] 成功创建 OpenAI 兼容提供商`, {
        providerType,
        subService,
        providerName: provider.name
      });

      return provider;
    } catch (error) {
      logger.error(`[TranslationService] 创建 OpenAI 兼容提供商失败`, {
        providerType,
        subService,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * 单文本翻译
   */
  async translateText(
    text: string,
    sourceLang: LanguageCode,
    targetLang: LanguageCode,
    contentType: 'title' | 'description' | 'selling_point' = 'title',
    providerName?: string,
    platform?: string,
    source?: string
  ): Promise<TranslationResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;
    this.stats.lastRequestTime = new Date();

    try {
      const provider = this.getProvider(providerName, contentType, sourceLang, targetLang, platform, source);

      const request: TranslationRequest = {
        text,
        sourceLang,
        targetLang,
        contentType
      };

      logger.debug(`Translation request:`, {
        provider: provider.name,
        sourceLang,
        targetLang,
        contentType,
        textLength: text.length
      });

      // 检查是否是 OpenAI 兼容提供商
      let result;
      if (provider instanceof OpenAICompatibleProvider) {
        // OpenAI 兼容提供商使用新的接口
        const multiResult = await provider.translateText(request.text, request.sourceLang, [request.targetLang], request.contentType);
        if (multiResult.success && multiResult.translations) {
          const targetLangKey = request.targetLang.toUpperCase();
          result = {
            success: true,
            text: multiResult.translations[targetLangKey],
            sourceLang: request.sourceLang,
            targetLang: request.targetLang
          };
        } else {
          result = {
            success: false,
            error: multiResult.error
          };
        }
      } else {
        // 传统提供商使用原有接口
        result = await provider.translateText(request);
      }
      
      // 更新统计信息
      const responseTime = Date.now() - startTime;
      this.updateStats(result.success, responseTime);

      logger.debug(`Translation result:`, {
        success: result.success,
        responseTime: `${responseTime}ms`,
        error: result.error
      });

      return result;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateStats(false, responseTime);

      logger.error('Translation service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '翻译服务异常'
      };
    }
  }
  
  /**
   * 批量翻译
   */
  async translateBatch(
    text: string,
    sourceLang: LanguageCode,
    targetLangs: LanguageCode[],
    contentType: 'title' | 'description' | 'selling_point' = 'title',
    providerName?: string,
    platform?: string,
    source?: string
  ): Promise<BatchTranslationResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;
    this.stats.lastRequestTime = new Date();

    try {
      const provider = this.getProvider(providerName, contentType, sourceLang, targetLangs[0], platform, source);

      const request: BatchTranslationRequest = {
        text,
        sourceLang,
        targetLangs,
        contentType
      };

      logger.debug(`收到批量翻译请求:`, {
        provider: provider.name,
        sourceLang,
        targetLangs,
        contentType,
        textLength: text.length
      });

      // 检查是否是 OpenAI 兼容提供商
      let result;
      if (provider instanceof OpenAICompatibleProvider) {
        // OpenAI 兼容提供商使用文本翻译方法
        const multiResult = await provider.translateText(request.text, request.sourceLang, request.targetLangs, request.contentType);
        result = {
          success: multiResult.success,
          translations: multiResult.translations,
          errors: multiResult.error ? { 'general': multiResult.error } : undefined
        };
      } else {
        // 传统提供商使用批量翻译方法
        result = await provider.translateBatch(request);
      }

      // 更新统计信息
      const responseTime = Date.now() - startTime;
      this.updateStats(result.success, responseTime);

      return result;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateStats(false, responseTime);

      logger.error('Batch translation service error:', error);

      // 统一返回配置错误信息
      return {
        success: false,
        errors: { 'config': error instanceof Error ? error.message : '无法配置翻译，请检查重试' }
      };
    }
  }

  /**
   * 产品批量翻译 - 基于产品ID列表
   */
  async translateProductBatch(
    productIds: number[],
    sourceLang: LanguageCode,
    targetLangs: LanguageCode[],
    platform: string,
    source: string,
    providerName?: string
  ): Promise<{
    success: boolean;
    message: string;
    results: {
      total: number;
      successful: number;
      failed: number;
      details: Array<{
        productId: number;
        sku?: string;
        success: boolean;
        error?: string;
        tokenUsage?: {
          inputTokens: number;
          outputTokens: number;
          totalTokens: number;
        };
      }>;
    };
  }> {
    const startTime = Date.now();
    this.stats.totalRequests++;
    this.stats.lastRequestTime = new Date();

    try {
      // 平台翻译配置
      const platformTranslationConfig = {
        worten: {
          needsTitle: true,
          needsDescription: true,
          needsSellingPoints: false  // Worten 不需要卖点
        }
      };

      const config = platformTranslationConfig[platform as keyof typeof platformTranslationConfig] || {
        needsTitle: true,
        needsDescription: true,
        needsSellingPoints: true  // 默认需要卖点
      };

      logger.info('[TranslationService] 开始产品批量翻译', {
        productCount: productIds.length,
        sourceLang,
        targetLangs,
        platform,
        source,
        providerName,
        translationConfig: config
      });

      // 获取产品数据
      const productDataList = await this.getProductDataForTranslation(productIds);

      if (productDataList.length === 0) {
        return {
          success: false,
          message: '未找到任何有效的产品数据',
          results: {
            total: productIds.length,
            successful: 0,
            failed: productIds.length,
            details: productIds.map(id => ({
              productId: id,
              success: false,
              error: '产品不存在或缺少必要数据'
            }))
          }
        };
      }

      // 获取翻译提供商
      const provider = this.getProvider(providerName, 'title', sourceLang, targetLangs[0], platform, source);

      const results: any[] = [];
      let successful = 0;
      let failed = 0;
      let totalTokenUsage = {
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0
      };

      // 处理每个产品的翻译
      for (const productData of productDataList) {
        try {
          logger.debug(`[TranslationService] 翻译产品 ${productData.sku}`, {
            productId: productData.id,
            sku: productData.sku
          });

          // 检查是否是 OpenAI 兼容提供商
          if (provider instanceof OpenAICompatibleProvider) {
            // OpenAI 兼容提供商 - 按字段分别翻译，每个字段一次性翻译所有目标语言
            const openaiResults = await this.translateProductWithOpenAI(
              provider,
              productData,
              sourceLang,
              targetLangs,
              config
            );

            if (openaiResults.success && openaiResults.translations) {
              // 更新数据库
              await this.updateProductTranslationMultiLang(
                productData.id,
                openaiResults.translations,
                targetLangs,
                config
              );

              successful++;
              if (openaiResults.tokenUsage) {
                totalTokenUsage.inputTokens += openaiResults.tokenUsage.inputTokens;
                totalTokenUsage.outputTokens += openaiResults.tokenUsage.outputTokens;
                totalTokenUsage.totalTokens += openaiResults.tokenUsage.totalTokens;
              }

              results.push({
                productId: productData.id,
                sku: productData.sku,
                success: true,
                tokenUsage: openaiResults.tokenUsage
              });
            } else {
              failed++;
              results.push({
                productId: productData.id,
                sku: productData.sku,
                success: false,
                error: openaiResults.error || 'OpenAI 翻译失败'
              });
            }
          } else {
            // MTran 提供商 - 需要分别处理每个字段和语言
            const mtranResults = await this.translateProductWithMTran(
              provider,
              productData,
              sourceLang,
              targetLangs,
              config
            );

            if (mtranResults.success && mtranResults.translations) {
              // 更新数据库 - MTran 使用多语言更新方法
              await this.updateProductTranslationMultiLang(
                productData.id,
                mtranResults.translations,
                targetLangs,
                config
              );

              successful++;
              results.push({
                productId: productData.id,
                sku: productData.sku,
                success: true
              });
            } else {
              failed++;
              results.push({
                productId: productData.id,
                sku: productData.sku,
                success: false,
                error: mtranResults.error || '翻译失败'
              });
            }
          }

        } catch (error) {
          failed++;
          logger.error(`[TranslationService] 产品 ${productData.sku} 翻译失败:`, error);
          results.push({
            productId: productData.id,
            sku: productData.sku,
            success: false,
            error: error instanceof Error ? error.message : '翻译异常'
          });
        }
      }

      // 更新统计信息
      const responseTime = Date.now() - startTime;
      this.updateStats(successful > 0, responseTime);

      const finalResult = {
        success: successful > 0,
        message: `批量翻译完成，成功 ${successful} 个，失败 ${failed} 个`,
        results: {
          total: productDataList.length,
          successful,
          failed,
          details: results
        }
      };

      logger.info('[TranslationService] 产品批量翻译完成', {
        total: productDataList.length,
        successful,
        failed,
        duration: responseTime
      });

      return finalResult;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateStats(false, responseTime);

      logger.error('[TranslationService] 产品批量翻译服务异常:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '产品批量翻译服务异常',
        results: {
          total: productIds.length,
          successful: 0,
          failed: productIds.length,
          details: productIds.map(id => ({
            productId: id,
            success: false,
            error: '服务异常'
          }))
        }
      };
    }
  }

  /**
   * 产品翻译（标题 + 描述）
   */
  async translateProduct(request: ProductTranslationRequest, providerName?: string): Promise<ProductTranslationResponse> {
    try {
      logger.info(`Product translation request for ${request.targetLangs.length} languages`);

      const titlePromise = this.translateBatch(
        request.title,
        request.sourceLang,
        request.targetLangs,
        'title',
        providerName
      );

      const descriptionPromise = this.translateBatch(
        request.description,
        request.sourceLang,
        request.targetLangs,
        'description',
        providerName
      );

      const [titleResult, descriptionResult] = await Promise.all([titlePromise, descriptionPromise]);

      const errors: string[] = [];
      
      if (!titleResult.success && titleResult.errors) {
        errors.push(`标题翻译失败: ${Object.values(titleResult.errors).join(', ')}`);
      }
      
      if (!descriptionResult.success && descriptionResult.errors) {
        errors.push(`描述翻译失败: ${Object.values(descriptionResult.errors).join(', ')}`);
      }

      return {
        success: titleResult.success || descriptionResult.success,
        title: titleResult.translations,
        description: descriptionResult.translations,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      logger.error('Product translation service error:', error);
      return {
        success: false,
        errors: [error instanceof Error ? error.message : '产品翻译服务异常']
      };
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(success: boolean, responseTime: number): void {
    if (success) {
      this.stats.successCount++;
    } else {
      this.stats.failureCount++;
    }

    // 计算平均响应时间
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests;
  }

  /**
   * 获取统计信息
   */
  public getStats(): TranslationStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      lastRequestTime: new Date()
    };
  }

  /**
   * 健康检查
   */
  async healthCheck(providerName?: string): Promise<{ provider: string; healthy: boolean; error?: string }> {
    try {
      const provider = this.getProvider(providerName);
      const healthy = await provider.healthCheck();
      
      return {
        provider: provider.name,
        healthy
      };
    } catch (error) {
      return {
        provider: providerName || 'unknown',
        healthy: false,
        error: error instanceof Error ? error.message : '健康检查异常'
      };
    }
  }

  /**
   * 获取可用的提供商列表
   */
  public getAvailableProviders(): string[] {
    this.ensureProvidersInitialized();
    return Array.from(this.providers.keys());
  }

  /**
   * 获取翻译配置
   */
  public getConfig() {
    return this.config.getConfig();
  }

  /**
   * 大批量翻译优化方案
   * 适用于200个SKU的批量翻译场景
   */
  public async translateLargeBatch(
    requests: Array<{
      text: string;
      sourceLang: LanguageCode;
      targetLang: LanguageCode;
      contentType: 'title' | 'description' | 'selling_point';
      identifier: string; // SKU
    }>,
    options: {
      concurrency?: number;      // 并发数，默认5
      batchSize?: number;        // 批次大小，默认50
      retryCount?: number;       // 重试次数，默认2
      progressCallback?: (progress: {
        total: number;
        completed: number;
        failed: number;
        current?: string;
        percentage: number;
      }) => void;
    } = {}
  ): Promise<{
    success: boolean;
    results: Array<{
      identifier: string;
      contentType: string;
      targetLang: string;
      success: boolean;
      text?: string;
      error?: string;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
      duration: number;
    };
  }> {
    const startTime = Date.now();
    const {
      concurrency = 5,
      batchSize = 50,
      retryCount = 2,
      progressCallback
    } = options;

    logger.info(`Starting large batch translation:`, {
      totalRequests: requests.length,
      concurrency,
      batchSize,
      retryCount
    });

    const results: any[] = [];
    let completed = 0;
    let failed = 0;

    // 分批处理，避免一次性处理过多请求
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      logger.info(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(requests.length / batchSize)}`);

      // 并发处理当前批次
      const batchResults = await this.processBatchWithConcurrency(
        batch,
        concurrency,
        retryCount,
        (batchProgress) => {
          const totalProgress = {
            total: requests.length,
            completed: completed + batchProgress.completed,
            failed: failed + batchProgress.failed,
            current: batchProgress.current,
            percentage: Math.round(((completed + batchProgress.completed) / requests.length) * 100)
          };
          progressCallback?.(totalProgress);
        }
      );

      results.push(...batchResults);
      completed += batchResults.filter(r => r.success).length;
      failed += batchResults.filter(r => !r.success).length;

      // 批次间休息，避免服务器压力过大
      if (i + batchSize < requests.length) {
        await this.sleep(500); // 500ms休息
      }
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    const summary = {
      total: requests.length,
      successful: completed,
      failed: failed,
      duration
    };

    logger.info(`Large batch translation completed:`, summary);

    return {
      success: failed === 0,
      results,
      summary
    };
  }

  /**
   * 并发处理批次
   */
  private async processBatchWithConcurrency(
    requests: any[],
    concurrency: number,
    retryCount: number,
    progressCallback?: (progress: any) => void
  ): Promise<any[]> {
    const results: any[] = [];
    let completed = 0;
    let failed = 0;

    const processRequest = async (request: any): Promise<any> => {
      let lastError: any;

      // 重试机制
      for (let attempt = 1; attempt <= retryCount; attempt++) {
        try {
          progressCallback?.({
            completed,
            failed,
            current: `翻译 ${request.identifier} 的 ${request.contentType} (尝试 ${attempt}/${retryCount})`
          });

          const result = await this.translateBatch(
            request.text,
            request.sourceLang,
            [request.targetLang],
            request.contentType
          );

          if (result.success && result.translations && result.translations[request.targetLang]) {
            completed++;
            return {
              identifier: request.identifier,
              contentType: request.contentType,
              targetLang: request.targetLang,
              success: true,
              text: result.translations[request.targetLang]
            };
          } else {
            throw new Error(result.errors?.[request.targetLang] || 'Translation failed');
          }
        } catch (error: any) {
          lastError = error;
          if (attempt < retryCount) {
            await this.sleep(1000 * attempt); // 递增延迟
          }
        }
      }

      failed++;
      return {
        identifier: request.identifier,
        contentType: request.contentType,
        targetLang: request.targetLang,
        success: false,
        error: lastError?.message || 'Unknown error'
      };
    };

    // 并发执行
    const chunks = [];
    for (let i = 0; i < requests.length; i += concurrency) {
      chunks.push(requests.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(processRequest);
      const chunkResults = await Promise.all(chunkPromises);
      results.push(...chunkResults);
    }

    return results;
  }

  /**
   * 使用 OpenAI 兼容提供商翻译产品
   */
  private async translateProductWithOpenAI(
    provider: any,
    productData: {
      id: number;
      sku: string;
      english_title: string;
      english_description: string;
      selling_point: string[];
    },
    sourceLang: LanguageCode,
    targetLangs: LanguageCode[],
    config: {
      needsTitle: boolean;
      needsDescription: boolean;
      needsSellingPoints: boolean;
    }
  ): Promise<{
    success: boolean;
    translations?: {
      title: Record<string, string>;
      description: Record<string, string>;
      sellingPoints: Record<string, string[]>;
    };
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
    error?: string;
  }> {
    try {
      const translations = {
        title: {} as Record<string, string>,
        description: {} as Record<string, string>,
        sellingPoints: {} as Record<string, string[]>
      };

      let totalTokenUsage = {
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0
      };

      // 翻译标题 - 一次性翻译所有目标语言
      if (config.needsTitle && productData.english_title) {
        const titleResult = await provider.translateText(
          productData.english_title,
          sourceLang,
          targetLangs, // 传入所有目标语言
          'title'
        );

        if (titleResult.success && titleResult.translations) {
          // titleResult.translations 应该是 { pt: "...", es: "..." } 格式
          Object.entries(titleResult.translations).forEach(([langCode, translatedText]) => {
            translations.title[langCode.toUpperCase()] = translatedText as string;
          });

          if (titleResult.tokenUsage) {
            totalTokenUsage.inputTokens += titleResult.tokenUsage.inputTokens;
            totalTokenUsage.outputTokens += titleResult.tokenUsage.outputTokens;
            totalTokenUsage.totalTokens += titleResult.tokenUsage.totalTokens;
          }
        } else {
          logger.warn(`产品 ${productData.sku} 标题翻译失败:`, titleResult.error);
        }
      }

      // 翻译描述 - 一次性翻译所有目标语言
      if (config.needsDescription && productData.english_description) {
        const descriptionResult = await provider.translateText(
          productData.english_description,
          sourceLang,
          targetLangs, // 传入所有目标语言
          'description'
        );

        if (descriptionResult.success && descriptionResult.translations) {
          Object.entries(descriptionResult.translations).forEach(([langCode, translatedText]) => {
            translations.description[langCode.toUpperCase()] = translatedText as string;
          });

          if (descriptionResult.tokenUsage) {
            totalTokenUsage.inputTokens += descriptionResult.tokenUsage.inputTokens;
            totalTokenUsage.outputTokens += descriptionResult.tokenUsage.outputTokens;
            totalTokenUsage.totalTokens += descriptionResult.tokenUsage.totalTokens;
          }
        } else {
          logger.warn(`产品 ${productData.sku} 描述翻译失败:`, descriptionResult.error);
        }
      }

      // 翻译卖点 - 一次性翻译所有目标语言
      if (config.needsSellingPoints && productData.selling_point && productData.selling_point.length > 0) {
        const sellingPointsText = productData.selling_point.filter(point => point.trim()).join('\n');
        if (sellingPointsText) {
          const sellingPointsResult = await provider.translateText(
            sellingPointsText,
            sourceLang,
            targetLangs, // 传入所有目标语言
            'selling_point'
          );

          if (sellingPointsResult.success && sellingPointsResult.translations) {
            Object.entries(sellingPointsResult.translations).forEach(([langCode, translatedText]) => {
              // 将翻译结果按行分割为数组
              const translatedPoints = (translatedText as string).split('\n').filter((point: string) => point.trim());
              // 确保有5个卖点，不足的用空字符串补充
              while (translatedPoints.length < 5) {
                translatedPoints.push('');
              }
              translations.sellingPoints[langCode.toUpperCase()] = translatedPoints.slice(0, 5);
            });

            if (sellingPointsResult.tokenUsage) {
              totalTokenUsage.inputTokens += sellingPointsResult.tokenUsage.inputTokens;
              totalTokenUsage.outputTokens += sellingPointsResult.tokenUsage.outputTokens;
              totalTokenUsage.totalTokens += sellingPointsResult.tokenUsage.totalTokens;
            }
          } else {
            logger.warn(`产品 ${productData.sku} 卖点翻译失败:`, sellingPointsResult.error);
          }
        }
      }

      logger.info(`产品 ${productData.sku} OpenAI 翻译完成`, {
        titleTranslations: Object.keys(translations.title).length,
        descriptionTranslations: Object.keys(translations.description).length,
        sellingPointsTranslations: Object.keys(translations.sellingPoints).length,
        totalTokenUsage
      });

      return {
        success: true,
        translations,
        tokenUsage: totalTokenUsage
      };

    } catch (error) {
      logger.error(`产品 ${productData.sku} OpenAI 翻译异常:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'OpenAI 翻译异常'
      };
    }
  }

  /**
   * 使用 MTran 提供商翻译产品
   */
  private async translateProductWithMTran(
    provider: any,
    productData: {
      id: number;
      sku: string;
      english_title: string;
      english_description: string;
      selling_point: string[];
    },
    sourceLang: LanguageCode,
    targetLangs: LanguageCode[],
    config: {
      needsTitle: boolean;
      needsDescription: boolean;
      needsSellingPoints: boolean;
    }
  ): Promise<{
    success: boolean;
    translations?: {
      title: Record<string, string>;
      description: Record<string, string>;
      sellingPoints: Record<string, string[]>;
    };
    error?: string;
  }> {
    try {
      const translations = {
        title: {} as Record<string, string>,
        description: {} as Record<string, string>,
        sellingPoints: {} as Record<string, string[]>
      };

      // 为每种目标语言分别翻译每个字段
      for (const targetLang of targetLangs) {
        const targetLangKey = targetLang.toUpperCase();

        // 翻译标题
        if (config.needsTitle && productData.english_title) {
          const titleResult = await provider.translateText({
            text: productData.english_title,
            sourceLang,
            targetLang,
            contentType: 'title'
          });

          if (titleResult.success) {
            translations.title[targetLangKey] = titleResult.text;
          } else {
            logger.warn(`产品 ${productData.sku} 标题翻译失败 (${targetLang}):`, titleResult.error);
          }
        }

        // 翻译描述
        if (config.needsDescription && productData.english_description) {
          const descriptionResult = await provider.translateText({
            text: productData.english_description,
            sourceLang,
            targetLang,
            contentType: 'description'
          });

          if (descriptionResult.success) {
            translations.description[targetLangKey] = descriptionResult.text;
          } else {
            logger.warn(`产品 ${productData.sku} 描述翻译失败 (${targetLang}):`, descriptionResult.error);
          }
        }

        // 翻译卖点
        if (config.needsSellingPoints && productData.selling_point && productData.selling_point.length > 0) {
          const sellingPointsText = productData.selling_point.filter(point => point.trim()).join('\n');
          if (sellingPointsText) {
            const sellingPointsResult = await provider.translateText({
              text: sellingPointsText,
              sourceLang,
              targetLang,
              contentType: 'selling_point'
            });

            if (sellingPointsResult.success) {
              // 将翻译结果按行分割为数组
              const translatedPoints = sellingPointsResult.text.split('\n').filter((point: string) => point.trim());
              // 确保有5个卖点，不足的用空字符串补充
              while (translatedPoints.length < 5) {
                translatedPoints.push('');
              }
              translations.sellingPoints[targetLangKey] = translatedPoints.slice(0, 5);
            } else {
              logger.warn(`产品 ${productData.sku} 卖点翻译失败 (${targetLang}):`, sellingPointsResult.error);
            }
          }
        }
      }

      logger.info(`产品 ${productData.sku} MTran 翻译完成`, {
        titleTranslations: Object.keys(translations.title).length,
        descriptionTranslations: Object.keys(translations.description).length,
        sellingPointsTranslations: Object.keys(translations.sellingPoints).length
      });

      return {
        success: true,
        translations
      };

    } catch (error) {
      logger.error(`产品 ${productData.sku} MTran 翻译异常:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'MTran 翻译异常'
      };
    }
  }

  /**
   * 获取产品翻译数据
   */
  private async getProductDataForTranslation(productIds: number[]): Promise<any[]> {
    try {
      // 导入数据库模型
      const { uploadproductModel } = await import('@/models/uploadproduct/uploadproductModel');

      const productDataList = [];

      for (const productId of productIds) {
        try {
          // 获取产品列表数据（包含关联的 product_dropship 数据）
          const productData = await uploadproductModel.getListingById(productId);

          if (productData && productData.english_title) {
            // 解析 selling_point JSON 字段
            let sellingPoints = [];
            if (productData.selling_point) {
              try {
                sellingPoints = typeof productData.selling_point === 'string'
                  ? JSON.parse(productData.selling_point)
                  : productData.selling_point;
              } catch (e) {
                logger.warn(`解析产品 ${productId} 的 selling_point 失败:`, e);
                sellingPoints = [];
              }
            }

            productDataList.push({
              id: productData.id,
              sku: productData.sku,
              english_title: productData.english_title,
              english_description: productData.english_description || '',
              selling_point: Array.isArray(sellingPoints) ? sellingPoints : []
            });
          } else {
            logger.warn(`产品 ${productId} 缺少必要的翻译数据`);
          }
        } catch (error) {
          logger.error(`获取产品 ${productId} 数据失败:`, error);
        }
      }

      logger.info(`成功获取 ${productDataList.length}/${productIds.length} 个产品的翻译数据`);
      return productDataList;

    } catch (error) {
      logger.error('获取产品翻译数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新产品翻译结果到数据库
   */
  private async updateProductTranslation(
    productId: number,
    translationResult: {
      title: string;
      description: string;
      sellingPoints: string[];
    },
    targetLangs: LanguageCode[],
    config?: {
      needsTitle: boolean;
      needsDescription: boolean;
      needsSellingPoints: boolean;
    }
  ): Promise<void> {
    try {
      // 导入数据库模型
      const { uploadproductModel } = await import('@/models/uploadproduct/uploadproductModel');

      // 获取当前的多语言数据
      const currentData = await uploadproductModel.getListingById(productId);
      if (!currentData) {
        throw new Error(`产品 ${productId} 不存在`);
      }

      // 解析现有的多语言数据
      let multiTitles: Record<string, string> = {};
      let multiDescriptions: Record<string, string> = {};
      let multiSellingPoints: Record<string, string[]> = {};

      try {
        multiTitles = typeof currentData.multi_titles === 'string'
          ? JSON.parse(currentData.multi_titles)
          : (currentData.multi_titles || {});
      } catch (e) {
        multiTitles = {};
      }

      try {
        multiDescriptions = typeof currentData.multi_descriptions === 'string'
          ? JSON.parse(currentData.multi_descriptions)
          : (currentData.multi_descriptions || {});
      } catch (e) {
        multiDescriptions = {};
      }

      try {
        multiSellingPoints = typeof currentData.multi_selling_point === 'string'
          ? JSON.parse(currentData.multi_selling_point)
          : (currentData.multi_selling_point || {});
      } catch (e) {
        multiSellingPoints = {};
      }

      // 根据配置更新翻译结果（目前只支持单个目标语言）
      const targetLang = targetLangs[0].toUpperCase();

      // 默认配置：如果没有传入配置，则更新所有字段
      const updateConfig = config || {
        needsTitle: true,
        needsDescription: true,
        needsSellingPoints: true
      };

      if (updateConfig.needsTitle) {
        multiTitles[targetLang] = translationResult.title;
      }

      if (updateConfig.needsDescription) {
        multiDescriptions[targetLang] = translationResult.description;
      }

      if (updateConfig.needsSellingPoints) {
        multiSellingPoints[targetLang] = translationResult.sellingPoints;
      }

      // 根据配置构建更新语句
      const updateFields = [];
      const updateParams = [];

      if (updateConfig.needsTitle) {
        updateFields.push('multi_titles = ?');
        updateParams.push(JSON.stringify(multiTitles));
      }

      if (updateConfig.needsDescription) {
        updateFields.push('multi_descriptions = ?');
        updateParams.push(JSON.stringify(multiDescriptions));
      }

      if (updateConfig.needsSellingPoints) {
        updateFields.push('multi_selling_point = ?');
        updateParams.push(JSON.stringify(multiSellingPoints));
      }

      // 只更新翻译状态，不更新上架时间
      updateFields.push('listings_translation_status = ?');
      updateParams.push('completed');

      // 添加 WHERE 条件参数
      updateParams.push(productId);

      // 构建并执行更新查询
      const updateQuery = `
        UPDATE uploadproduct_listings
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      await uploadproductModel.query(updateQuery, updateParams);

      logger.info(`产品 ${productId} 翻译结果已更新到数据库`);

    } catch (error) {
      logger.error(`更新产品 ${productId} 翻译结果失败:`, error);
      throw error;
    }
  }

  /**
   * 更新产品多语言翻译结果到数据库（用于 MTran 提供商）
   */
  private async updateProductTranslationMultiLang(
    productId: number,
    translations: {
      title: Record<string, string>;
      description: Record<string, string>;
      sellingPoints: Record<string, string[]>;
    },
    targetLangs: LanguageCode[],
    config?: {
      needsTitle: boolean;
      needsDescription: boolean;
      needsSellingPoints: boolean;
    }
  ): Promise<void> {
    try {
      // 导入数据库模型
      const { uploadproductModel } = await import('@/models/uploadproduct/uploadproductModel');

      // 获取当前的多语言数据
      const currentData = await uploadproductModel.getListingById(productId);
      if (!currentData) {
        throw new Error(`产品 ${productId} 不存在`);
      }

      // 解析现有的多语言数据
      let multiTitles: Record<string, string> = {};
      let multiDescriptions: Record<string, string> = {};
      let multiSellingPoints: Record<string, string[]> = {};

      try {
        multiTitles = typeof currentData.multi_titles === 'string'
          ? JSON.parse(currentData.multi_titles)
          : (currentData.multi_titles || {});
      } catch (e) {
        multiTitles = {};
      }

      try {
        multiDescriptions = typeof currentData.multi_descriptions === 'string'
          ? JSON.parse(currentData.multi_descriptions)
          : (currentData.multi_descriptions || {});
      } catch (e) {
        multiDescriptions = {};
      }

      try {
        multiSellingPoints = typeof currentData.multi_selling_point === 'string'
          ? JSON.parse(currentData.multi_selling_point)
          : (currentData.multi_selling_point || {});
      } catch (e) {
        multiSellingPoints = {};
      }

      // 默认配置：如果没有传入配置，则更新所有字段
      const updateConfig = config || {
        needsTitle: true,
        needsDescription: true,
        needsSellingPoints: true
      };

      // 合并翻译结果
      if (updateConfig.needsTitle) {
        Object.assign(multiTitles, translations.title);
      }

      if (updateConfig.needsDescription) {
        Object.assign(multiDescriptions, translations.description);
      }

      if (updateConfig.needsSellingPoints) {
        Object.assign(multiSellingPoints, translations.sellingPoints);
      }

      // 根据配置构建更新语句
      const updateFields = [];
      const updateParams = [];

      if (updateConfig.needsTitle) {
        updateFields.push('multi_titles = ?');
        updateParams.push(JSON.stringify(multiTitles));
      }

      if (updateConfig.needsDescription) {
        updateFields.push('multi_descriptions = ?');
        updateParams.push(JSON.stringify(multiDescriptions));
      }

      if (updateConfig.needsSellingPoints) {
        updateFields.push('multi_selling_point = ?');
        updateParams.push(JSON.stringify(multiSellingPoints));
      }

      // 只更新翻译状态，不更新上架时间
      updateFields.push('listings_translation_status = ?');
      updateParams.push('completed');

      // 添加 WHERE 条件参数
      updateParams.push(productId);

      // 构建并执行更新查询
      const updateQuery = `
        UPDATE uploadproduct_listings
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      await uploadproductModel.query(updateQuery, updateParams);

      logger.info(`产品 ${productId} 多语言翻译结果已更新到数据库`, {
        titleLanguages: Object.keys(translations.title),
        descriptionLanguages: Object.keys(translations.description),
        sellingPointsLanguages: Object.keys(translations.sellingPoints)
      });

    } catch (error) {
      logger.error(`更新产品 ${productId} 多语言翻译结果失败:`, error);
      throw error;
    }
  }

  /**
   * 延迟函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 延迟初始化的单例实例
let translationServiceInstance: TranslationService | null = null;

export const getTranslationService = (): TranslationService => {
  if (!translationServiceInstance) {
    translationServiceInstance = TranslationService.getInstance();
  }
  return translationServiceInstance;
};

// 为了向后兼容，保留原有的导出方式，但使用延迟初始化
export const translationService = new Proxy({} as TranslationService, {
  get(_target, prop) {
    const service = getTranslationService();
    const value = (service as any)[prop];
    return typeof value === 'function' ? value.bind(service) : value;
  }
});
