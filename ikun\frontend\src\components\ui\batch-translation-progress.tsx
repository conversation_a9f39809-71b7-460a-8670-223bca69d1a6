"use client"

import * as React from "react"
import { ProgressDialog, ProgressItem } from "./progress-dialog"

export interface TranslationProgressItem {
  productId: number
  sku: string
  name: string
  status: 'pending' | 'processing' | 'success' | 'error'
  message?: string
  error?: string
}

export interface BatchTranslationProgressProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  items: TranslationProgressItem[]
  isProcessing: boolean
  onCancel?: () => void
  onComplete?: () => void
}

export function BatchTranslationProgress({
  open,
  onOpenChange,
  items,
  isProcessing,
  onCancel,
  onComplete
}: BatchTranslationProgressProps) {
  const [progress, setProgress] = React.useState(0)

  // 转换为通用进度项格式
  const progressItems: ProgressItem[] = items.map(item => ({
    id: item.productId,
    name: `${item.sku} - ${item.name}`,
    status: item.status,
    message: item.message,
    error: item.error
  }))

  // 计算进度
  React.useEffect(() => {
    const completedCount = items.filter(item => 
      item.status === 'success' || item.status === 'error'
    ).length
    const newProgress = items.length > 0 ? (completedCount / items.length) * 100 : 0
    setProgress(newProgress)

    // 如果全部完成且不在处理中，触发完成回调
    if (completedCount === items.length && items.length > 0 && !isProcessing) {
      setTimeout(() => {
        onComplete?.()
      }, 1000) // 延迟1秒让用户看到完成状态
    }
  }, [items, isProcessing, onComplete])

  const successCount = items.filter(item => item.status === 'success').length
  const errorCount = items.filter(item => item.status === 'error').length

  const getSuccessMessage = () => {
    if (successCount === items.length) {
      return `所有 ${items.length} 个产品翻译完成！`
    }
    if (successCount > 0) {
      return `成功翻译 ${successCount} 个产品`
    }
    return undefined
  }

  const getErrorMessage = () => {
    if (errorCount === items.length) {
      return `所有 ${items.length} 个产品翻译失败，请检查后重试`
    }
    if (errorCount > 0) {
      return `${errorCount} 个产品翻译失败`
    }
    return undefined
  }

  return (
    <ProgressDialog
      open={open}
      onOpenChange={onOpenChange}
      title="批量翻译进度"
      items={progressItems}
      progress={progress}
      isProcessing={isProcessing}
      canCancel={true}
      onCancel={onCancel}
      showDetails={true}
      successMessage={getSuccessMessage()}
      errorMessage={getErrorMessage()}
    />
  )
}

// Hook for managing batch translation progress
export function useBatchTranslationProgress() {
  const [items, setItems] = React.useState<TranslationProgressItem[]>([])
  const [isProcessing, setIsProcessing] = React.useState(false)
  const [isOpen, setIsOpen] = React.useState(false)

  const startTranslation = (productList: Array<{ id: number; sku: string; name: string }>) => {
    const initialItems: TranslationProgressItem[] = productList.map(product => ({
      productId: product.id,
      sku: product.sku,
      name: product.name,
      status: 'pending',
      message: '等待翻译...'
    }))
    
    setItems(initialItems)
    setIsProcessing(true)
    setIsOpen(true)
  }

  const updateItemStatus = (
    productId: number, 
    status: TranslationProgressItem['status'], 
    message?: string, 
    error?: string
  ) => {
    setItems(prev => prev.map(item => 
      item.productId === productId 
        ? { ...item, status, message, error }
        : item
    ))
  }

  const setProcessingItem = (productId: number) => {
    updateItemStatus(productId, 'processing', '正在翻译...')
  }

  const setSuccessItem = (productId: number, message?: string) => {
    updateItemStatus(productId, 'success', message || '翻译完成')
  }

  const setErrorItem = (productId: number, error: string) => {
    updateItemStatus(productId, 'error', undefined, error)
  }

  const finishTranslation = () => {
    setIsProcessing(false)
  }

  const cancelTranslation = () => {
    setIsProcessing(false)
    setIsOpen(false)
    setItems([])
  }

  const closeDialog = () => {
    setIsOpen(false)
    setItems([])
  }

  return {
    items,
    isProcessing,
    isOpen,
    startTranslation,
    setProcessingItem,
    setSuccessItem,
    setErrorItem,
    finishTranslation,
    cancelTranslation,
    closeDialog,
    setIsOpen
  }
}
