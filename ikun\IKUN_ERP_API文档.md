# IKUN ERP API 接口文档

## 📚 文档信息
- **项目名称**: IKUN ERP 系统
- **API版本**: v1
- **基础URL**: `http://localhost:3001/api/v1`
- **文档版本**: 1.0
- **最后更新**: 2024年1月

## 🔗 快速导航

### API分类导航

#### 🔐 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新Token
- `GET /api/v1/auth/profile` - 获取用户信息

#### 📁 产品目录管理
- `GET /api/v1/categories` - 获取目录列表
- `GET /api/v1/categories/tree` - 获取目录树
- `POST /api/v1/categories` - 创建目录
- `PUT /api/v1/categories/:id` - 更新目录
- `DELETE /api/v1/categories/:id` - 删除目录
- `POST /api/v1/categories/batch` - 批量创建目录

#### 🏪 店铺管理
- `GET /api/v1/stores` - 获取店铺列表
- `POST /api/v1/stores` - 创建店铺
- `GET /api/v1/stores/:id` - 获取店铺详情
- `PUT /api/v1/stores/:id` - 更新店铺
- `DELETE /api/v1/stores/:id` - 删除店铺
- `GET /api/v1/platforms` - 获取平台列表

#### 📦 产品上架管理
- `GET /api/v1/uploadproduct/listings` - 获取产品上架列表
- `POST /api/v1/uploadproduct/listings` - 创建产品上架
- `GET /api/v1/uploadproduct/listings/:id` - 获取上架详情
- `PUT /api/v1/uploadproduct/listings/:id` - 更新产品上架
- `DELETE /api/v1/uploadproduct/listings/:id` - 删除产品上架

#### 🌐 翻译服务
- `POST /api/v1/translation/text` - 单文本翻译
- `POST /api/v1/translation/batch/forediting` - 表单批量翻译
- `POST /api/v1/translation/batch/forbatch` - 产品批量翻译
- `GET /api/v1/translation/config` - 获取翻译配置
- `GET /api/v1/translation/stats` - 获取翻译统计
- `GET /api/v1/translation/health` - 翻译服务健康检查

#### 📦 产品管理
- `GET /api/v1/products` - 获取产品列表
- `POST /api/v1/products` - 创建产品
- `GET /api/v1/products/:id` - 获取产品详情
- `PUT /api/v1/products/:id` - 更新产品
- `DELETE /api/v1/products/:id` - 删除产品
- `POST /api/v1/products/import` - 批量导入产品

#### 🏪 店铺管理
- `GET /api/v1/stores` - 获取店铺列表
- `POST /api/v1/stores` - 创建店铺
- `GET /api/v1/stores/:id` - 获取店铺详情
- `PUT /api/v1/stores/:id` - 更新店铺
- `DELETE /api/v1/stores/:id` - 删除店铺

#### 📋 产品上架
- `GET /api/v1/uploadproduct` - 获取上架产品列表
- `POST /api/v1/uploadproduct` - 创建上架产品
- `GET /api/v1/uploadproduct/:id` - 获取上架产品详情
- `PUT /api/v1/uploadproduct/:id` - 更新上架产品
- `DELETE /api/v1/uploadproduct/:id` - 删除上架产品

#### 🌍 翻译服务
- `POST /api/v1/translation/text` - 单文本翻译
- `POST /api/v1/translation/batch/forediting` - 表单批量翻译

#### 📤 文件上传
- `POST /api/v1/upload/images` - 上传产品图片
- `POST /api/v1/upload/images/batch` - 批量上传图片

#### 📊 统计分析
- `GET /api/v1/dashboard/stats` - 获取仪表板数据
- `GET /api/v1/stats/products` - 获取产品统计
- `GET /api/v1/stats/orders` - 获取订单统计

## 🔧 认证说明
所有API请求都需要在请求头中包含认证信息：
```http
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

## 📋 响应格式
所有API响应都遵循统一的格式：
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 📝 状态码说明
- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🛠️ 开发工具

### API测试工具
- **Postman**: 推荐使用Postman进行API测试
- **curl**: 命令行测试工具
- **Insomnia**: 轻量级API测试工具

### 开发环境配置
```bash
# 本地开发环境
BASE_URL=http://localhost:3001/api/v1

# 测试环境
BASE_URL=https://test-api.ikun-erp.com/api/v1

# 生产环境
BASE_URL=https://api.ikun-erp.com/api/v1
```

---

## 1. 认证相关API

### 1.1 用户登录
```http
POST /api/v1/auth/login
```

**请求参数:**
```json
{
  "username": "admin",
  "password": "password123"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
}
```

### 1.2 用户登出
```http
POST /api/v1/auth/logout
```

### 1.3 刷新Token
```http
POST /api/v1/auth/refresh
```

### 1.4 获取用户信息
```http
GET /api/v1/auth/profile
```

---

## 2. 产品目录相关API

### 2.1 获取产品目录列表
```http
GET /api/v1/categories
```

**查询参数:**
- `level`: 类目层级 (1/2/3)
- `parent_id`: 父级类目ID
- `status`: 状态 (enabled/disabled)
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "categories": [
      {
        "id": 1,
        "category_code": "CLO",
        "chinese_name": "服装",
        "english_name": "Clothing",
        "status": "enabled",
        "auto_sku": "enabled",
        "category_level": 1,
        "parent_id": null,
        "category_description": "服装类产品",
        "attribute_tags": ["color", "size"],
        "sort_order": 1,
        "category_path": "clothing",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

### 2.2 获取目录树结构
```http
GET /api/v1/categories/tree
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "label": "服装 (Clothing)",
      "value": "clothing",
      "level": 1,
      "children": [
        {
          "id": 4,
          "label": "男装 (Mens)",
          "value": "clothing/mens",
          "level": 2,
          "children": [
            {
              "id": 9,
              "label": "T恤 (Tshirts)",
              "value": "clothing/mens/tshirts",
              "level": 3,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}
```

### 2.3 创建产品目录
```http
POST /api/v1/categories
```

**请求参数:**
```json
{
  "category_code": "CLO-M-T",
  "chinese_name": "T恤",
  "english_name": "Tshirts",
  "status": "enabled",
  "auto_sku": "enabled",
  "category_level": 3,
  "parent_id": 4,
  "category_description": "男士T恤衫",
  "attribute_tags": ["color", "size"],
  "sort_order": 1
}
```

**响应示例:**
```json
{
  "code": 201,
  "message": "Category created successfully",
  "data": {
    "id": 15,
    "category_path": "clothing/mens/tshirts",
    "category_code": "CLO-M-T",
    "chinese_name": "T恤",
    "english_name": "Tshirts"
  }
}
```

### 2.4 获取产品目录详情
```http
GET /api/v1/categories/:id
```

### 2.5 更新产品目录
```http
PUT /api/v1/categories/:id
```

**请求参数:**
```json
{
  "chinese_name": "更新的中文名",
  "english_name": "Updated English Name",
  "status": "disabled",
  "category_description": "更新的描述",
  "attribute_tags": ["color", "size", "material"]
}
```

### 2.6 删除产品目录
```http
DELETE /api/v1/categories/:id
```

**响应示例:**
```json
{
  "code": 200,
  "message": "Category deleted successfully"
}
```

### 2.7 获取指定层级的目录
```http
GET /api/v1/categories/level/:level
```

### 2.8 批量创建目录
```http
POST /api/v1/categories/batch
```

**请求参数:**
```json
{
  "categories": [
    {
      "category_code": "CLO-M-S",
      "chinese_name": "衬衫",
      "english_name": "Shirts",
      "category_level": 3,
      "parent_id": 4,
      "attribute_tags": ["color", "size"]
    }
  ]
}
```

### 2.9 更新目录状态
```http
PUT /api/v1/categories/:id/status
```

**请求参数:**
```json
{
  "status": "disabled"
}
```

### 2.10 获取子目录
```http
GET /api/v1/categories/:id/children
```

---

## 3. 产品相关API

### 3.1 获取产品列表
```http
GET /api/v1/products
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `status`: 产品状态 (draft/active/inactive)
- `category_id`: 类目ID
- `source`: 产品来源
- `search`: 搜索关键词
- `start_date`: 开始日期
- `end_date`: 结束日期

### 3.2 创建产品
```http
POST /api/v1/products
```

**请求参数:**
```json
{
  "sku": "CLO-M-T-001",
  "ean": "1234567890123",
  "category_id": 9,
  "english_title": "Casual Cotton T-Shirt for Men",
  "english_description": "Comfortable and stylish cotton t-shirt",
  "image1": "/images/product1.jpg",
  "cost_price": 15.50,
  "package_weight": 200,
  "status": "active",
  "source": "手动添加"
}
```

### 3.3 获取产品详情
```http
GET /api/v1/products/:id
```

### 3.4 更新产品
```http
PUT /api/v1/products/:id
```

### 3.5 删除产品
```http
DELETE /api/v1/products/:id
```

### 3.6 批量导入产品
```http
POST /api/v1/products/import
```

### 3.7 获取指定目录下的产品
```http
GET /api/v1/products/category/:categoryId
```

---

## 4. 采集相关API

### 4.1 开始采集任务
```http
POST /api/v1/scraping/start
```

**请求参数:**
```json
{
  "platform": "amazon",
  "url": "https://www.amazon.com/product/123",
  "category_id": 9
}
```

### 4.2 获取采集任务列表
```http
GET /api/v1/scraping/tasks
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `status`: 任务状态 (pending/running/completed/failed)
- `platform`: 采集平台

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tasks": [
      {
        "id": "task-001",
        "platform": "amazon",
        "url": "https://www.amazon.com/product/123",
        "status": "completed",
        "result": {
          "title": "Product Title",
          "price": 29.99,
          "images": ["image1.jpg", "image2.jpg"]
        },
        "created_at": "2024-01-01T00:00:00Z",
        "completed_at": "2024-01-01T00:05:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20
  }
}
```

### 4.3 获取采集任务详情
```http
GET /api/v1/scraping/tasks/:id
```

### 4.4 删除采集任务
```http
DELETE /api/v1/scraping/tasks/:id
```

---

## 5. 订单相关API

### 5.1 获取订单列表
```http
GET /api/v1/orders
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `status`: 订单状态 (pending/paid/shipped/delivered/cancelled)
- `platform`: 平台名称
- `start_date`: 开始日期
- `end_date`: 结束日期

### 5.2 获取订单详情
```http
GET /api/v1/orders/:id
```

### 5.3 更新订单状态
```http
PUT /api/v1/orders/:id/status
```

**请求参数:**
```json
{
  "status": "shipped",
  "tracking_number": "1234567890"
}
```

### 5.4 同步平台订单
```http
POST /api/v1/orders/sync
```

**请求参数:**
```json
{
  "platform": "ebay",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

---

## 6. 平台相关API

### 6.1 获取支持的平台列表
```http
GET /api/v1/platforms
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "Amazon",
      "code": "amazon",
      "status": "active",
      "supported_features": ["product_sync", "order_sync", "inventory_sync"]
    },
    {
      "id": 2,
      "name": "eBay",
      "code": "ebay",
      "status": "active",
      "supported_features": ["product_sync", "order_sync"]
    }
  ]
}
```

### 6.2 获取平台配置
```http
GET /api/v1/platforms/:id/config
```

### 6.3 更新平台配置
```http
PUT /api/v1/platforms/:id/config
```

---

## 7. 文件上传API

### 7.1 上传产品图片
```http
POST /api/v1/upload/images
```

**请求格式:** `multipart/form-data`

**请求参数:**
- `file`: 图片文件 (支持 jpg, png, webp, gif)
- `type`: 文件类型 (product_image)

**响应示例:**
```json
{
  "code": 200,
  "message": "Upload successful",
  "data": {
    "url": "/uploads/images/product_20240101_123456.jpg",
    "filename": "product_20240101_123456.jpg",
    "size": 1024000,
    "mime_type": "image/jpeg"
  }
}
```

### 7.2 批量上传图片
```http
POST /api/v1/upload/images/batch
```

---

## 8. 统计分析API

### 8.1 获取仪表板数据
```http
GET /api/v1/dashboard/stats
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_products": 1250,
    "active_products": 980,
    "total_orders": 456,
    "pending_orders": 23,
    "total_revenue": 125000.50,
    "monthly_revenue": 15600.75
  }
}
```

### 8.2 获取产品统计
```http
GET /api/v1/stats/products
```

### 8.3 获取订单统计
```http
GET /api/v1/stats/orders
```

---

## 9. 系统配置API

### 9.1 获取系统配置
```http
GET /api/v1/system/config
```

### 9.2 更新系统配置
```http
PUT /api/v1/system/config
```

---

## 错误处理

### 错误响应格式
```json
{
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "sku",
      "message": "SKU is required"
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 常见错误码
- `400001`: 参数验证失败
- `401001`: Token无效或过期
- `403001`: 权限不足
- `404001`: 资源不存在
- `409001`: 资源冲突（如SKU重复）
- `500001`: 数据库连接失败
- `500002`: 外部服务调用失败

---

## 接口测试

### 使用Postman测试
1. 导入API集合文件
2. 设置环境变量：
   - `base_url`: http://localhost:3001/api/v1
   - `token`: 登录后获取的JWT token

### 使用curl测试示例
```bash
# 登录获取token
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'

# 获取产品目录列表
curl -X GET http://localhost:3001/api/v1/categories \
  -H "Authorization: Bearer <your-token>"

# 创建产品目录
curl -X POST http://localhost:3001/api/v1/categories \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{"category_code":"TEST","chinese_name":"测试","english_name":"Test","category_level":1}'
```

---

## 版本更新记录

### v1.0 (2024-01-01)
- 初始版本发布
- 完整的产品目录管理API
- 产品CRUD操作API
- 采集任务管理API
- 订单管理API
- 文件上传API
- 统计分析API

---

## 联系信息
- **开发团队**: IKUN ERP Team
- **技术支持**: <EMAIL>
- **文档更新**: 请关注项目仓库
