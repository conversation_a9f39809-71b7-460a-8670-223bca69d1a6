/**
 * Upload Product Model
 * Database operations for product listings
 */

import { database } from '@/database/connection';
import { getBeijingTimeISO } from '@/utils/time';
import { logger } from '@/utils/logger';

class UploadProductModel {
  // Execute raw SQL query
  public async query(sql: string, params: any[] = []): Promise<any[]> {
    try {
      const rows = await database.query(sql, params);
      return rows as any[];
    } catch (error) {
      logger.error('Database query error:', { sql, params, error });
      throw error;
    }
  }

  // Get listing by ID
  public async getListingById(id: number): Promise<any | null> {
    try {
      // 使用JOIN查询同时获取uploadproduct_listings和product_dropship表的数据
      const query = `
        SELECT
          l.id,
          l.dropship_product_id,
          l.store_id,
          l.platform_code,
          l.sku,
          l.ean,
          l.upstores_sku,
          l.upstores_ean,
          l.multi_titles,
          l.multi_descriptions,
          l.multi_selling_point,
          l.platform_category_id,
          l.platform_attributes,
          l.platform_data,
          l.discounted_price,
          l.original_price,
          l.discount_end_date,
          l.discount_start_date,
          l.stock_quantity,
          l.discount,
          l.currency,
          l.status,
          l.listing_id,
          l.listings_translation_status as translation_status,
          l.attributes_status,
          l.error_message,
          l.last_sync_at,
          l.created_at,
          l.uplisting_at,

          /* 从product_dropship表获取基本信息（优先使用product_dropship表的数据） */
          COALESCE(p.english_title, l.english_title) as english_title,
          p.english_description,
          p.selling_point,
          COALESCE(p.image1, l.image1) as image1,
          p.image2,
          p.image3,
          p.image4,
          p.image5,
          /* 确保即使product_dropship表没有数据，也能显示基本信息 */
          l.english_title as listing_english_title,
          l.image1 as listing_image1,
          p.category,
          p.cost_price,
          p.package_weight,
          p.package_length,
          p.package_width,
          p.package_height,
          p.purchase_link,
          p.remarks
        FROM uploadproduct_listings l
        LEFT JOIN product_dropship p ON l.dropship_product_id = p.id
        WHERE l.id = ?
      `;

      const results = await this.query(query, [id]);

      if (results.length > 0) {
        const listing = results[0];

        // 调试日志：检查关联数据
        logger.info('Product listing data retrieved:', {
          listingId: id,
          dropshipProductId: listing.dropship_product_id,
          hasEnglishDescription: !!listing.english_description,
          hasSellingPoint: !!listing.selling_point,
          hasImages: {
            image1: !!listing.image1,
            image2: !!listing.image2,
            image3: !!listing.image3,
            image4: !!listing.image4,
            image5: !!listing.image5
          }
        });

        // 字段名映射：将数据库字段名映射为前端期望的字段名
        if (listing.multi_selling_point !== undefined) {
          listing.multi_selling_points = listing.multi_selling_point;
          delete listing.multi_selling_point;
        }

        // 解析JSON字段
        if (typeof listing.selling_point === 'string') {
          try {
            listing.selling_point = JSON.parse(listing.selling_point);
          } catch (e) {
            listing.selling_point = [];
          }
        }

        return listing;
      }

      return null;
    } catch (error) {
      logger.error('Error getting listing by ID:', error);
      throw error;
    }
  }

  // Create new listing
  public async createListing(data: any): Promise<any> {
    try {
      const now = getBeijingTimeISO();
      
      const query = `
        INSERT INTO uploadproduct_listings (
          dropship_product_id,
          store_id,
          platform_code,
          sku,
          ean,
          english_title,
          image1,
          multi_titles,
          multi_descriptions,
          platform_category_id,
          platform_attributes,
          status,
          listings_translation_status,
          created_at,
          uplisting_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        data.dropship_product_id || null,
        data.store_id || null,
        data.platform_code,
        data.sku,
        data.ean,
        data.english_title,
        data.image1 || null,
        JSON.stringify(data.multi_titles || {}),
        JSON.stringify(data.multi_descriptions || {}),
        data.platform_category_id || null,
        JSON.stringify(data.platform_attributes || {}),
        data.status || 'draft',
        data.translation_status || 'pending',
        now,
        now
      ];

      const result = await database.executeUpdate(query, params);
      
      // Get the created listing
      return await this.getListingById(result.insertId);
    } catch (error) {
      logger.error('Error creating listing:', error);
      throw error;
    }
  }

  // Update listing
  public async updateListing(id: number, data: any): Promise<any> {
    try {
      const now = getBeijingTimeISO();
      
      const updateFields: string[] = [];
      const params: any[] = [];

      if (data.dropship_product_id !== undefined) {
        updateFields.push('dropship_product_id = ?');
        params.push(data.dropship_product_id);
      }

      if (data.store_id !== undefined) {
        updateFields.push('store_id = ?');
        params.push(data.store_id);
      }

      if (data.platform_code !== undefined) {
        updateFields.push('platform_code = ?');
        params.push(data.platform_code);
      }

      if (data.sku !== undefined) {
        updateFields.push('sku = ?');
        params.push(data.sku);
      }

      if (data.ean !== undefined) {
        updateFields.push('ean = ?');
        params.push(data.ean);
      }

      if (data.english_title !== undefined) {
        updateFields.push('english_title = ?');
        params.push(data.english_title);
      }

      if (data.multi_titles !== undefined) {
        updateFields.push('multi_titles = ?');
        params.push(JSON.stringify(data.multi_titles));
      }

      if (data.multi_descriptions !== undefined) {
        updateFields.push('multi_descriptions = ?');
        params.push(JSON.stringify(data.multi_descriptions));
      }

      if (data.platform_category_id !== undefined) {
        updateFields.push('platform_category_id = ?');
        params.push(data.platform_category_id);
      }

      if (data.platform_attributes !== undefined) {
        updateFields.push('platform_attributes = ?');
        params.push(JSON.stringify(data.platform_attributes));
      }

      // 新增：多语言卖点字段支持（注意：数据库字段名是 multi_selling_point）
      if (data.multi_selling_points !== undefined) {
        updateFields.push('multi_selling_point = ?');
        params.push(JSON.stringify(data.multi_selling_points));
      }

      // 新增：平台数据字段支持
      if (data.platform_data !== undefined) {
        updateFields.push('platform_data = ?');
        params.push(JSON.stringify(data.platform_data));
      }

      // 图片字段支持（目前数据库只有 image1 字段）
      if (data.image1 !== undefined) {
        updateFields.push('image1 = ?');
        params.push(data.image1);
      }

      // 注意：english_description 和 image2-5 字段在当前数据库表中不存在
      // 如果需要这些字段，需要先添加数据库迁移

      // 平台特定SKU和EAN
      if (data.upstores_sku !== undefined || data.platform_sku !== undefined) {
        updateFields.push('upstores_sku = ?');
        params.push(data.upstores_sku || data.platform_sku);
      }

      if (data.upstores_ean !== undefined || data.platform_ean !== undefined) {
        updateFields.push('upstores_ean = ?');
        params.push(data.upstores_ean || data.platform_ean);
      }

      // 价格相关字段
      if (data.discounted_price !== undefined) {
        updateFields.push('discounted_price = ?');
        params.push(data.discounted_price);
      }

      if (data.original_price !== undefined) {
        updateFields.push('original_price = ?');
        params.push(data.original_price);
      }

      if (data.stock_quantity !== undefined) {
        updateFields.push('stock_quantity = ?');
        params.push(data.stock_quantity);
      }

      if (data.discount !== undefined || data.discount_percentage !== undefined) {
        updateFields.push('discount = ?');
        params.push((data.discount || data.discount_percentage) / 100); // 转换为小数
      }

      if (data.discount_start_date !== undefined) {
        updateFields.push('discount_start_date = ?');
        params.push(data.discount_start_date || null);
      }

      if (data.discount_end_date !== undefined) {
        updateFields.push('discount_end_date = ?');
        params.push(data.discount_end_date || null);
      }

      if (data.currency !== undefined) {
        updateFields.push('currency = ?');
        params.push(data.currency);
      }

      // 状态字段
      if (data.status !== undefined) {
        updateFields.push('status = ?');
        params.push(data.status);
      }

      if (data.translation_status !== undefined || data.listings_translation_status !== undefined) {
        updateFields.push('listings_translation_status = ?');
        params.push(data.translation_status || data.listings_translation_status);
      }

      if (data.attributes_status !== undefined) {
        updateFields.push('attributes_status = ?');
        params.push(data.attributes_status);
      }

      // 其他字段
      if (data.listing_id !== undefined) {
        updateFields.push('listing_id = ?');
        params.push(data.listing_id);
      }

      if (data.error_message !== undefined) {
        updateFields.push('error_message = ?');
        params.push(data.error_message);
      }

      // 只在特定情况下更新 uplisting_at（如状态变更、价格变更等）
      // 翻译操作不应该更新上架时间
      const isTranslationOnlyUpdate = Object.keys(data).every(key =>
        ['multi_titles', 'multi_descriptions', 'multi_selling_points', 'listings_translation_status'].includes(key)
      );

      const shouldUpdateUplistingTime = !isTranslationOnlyUpdate && (
        data.status !== undefined ||
        data.discounted_price !== undefined ||
        data.original_price !== undefined ||
        data.stock_quantity !== undefined ||
        data.listing_id !== undefined ||
        data.platform_attributes !== undefined ||
        data.platform_data !== undefined
      );

      if (shouldUpdateUplistingTime) {
        updateFields.push('uplisting_at = ?');
        params.push(now);
      }

      params.push(id);

      const query = `
        UPDATE uploadproduct_listings 
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      await database.executeUpdate(query, params);
      
      // Get the updated listing
      return await this.getListingById(id);
    } catch (error) {
      logger.error('Error updating listing:', error);
      throw error;
    }
  }

  // Delete listing
  public async deleteListing(id: number): Promise<void> {
    try {
      const query = 'DELETE FROM uploadproduct_listings WHERE id = ?';
      await database.executeUpdate(query, [id]);
    } catch (error) {
      logger.error('Error deleting listing:', error);
      throw error;
    }
  }

  // Get categories
  public async getCategories(query: any): Promise<any[]> {
    try {
      const conditions: string[] = [];
      const params: any[] = [];

      if (query.platform_code) {
        conditions.push('platform_code = ?');
        params.push(query.platform_code);
      }

      if (query.parent_category_id) {
        conditions.push('parent_category_id = ?');
        params.push(parseInt(query.parent_category_id));
      }

      if (query.is_leaf !== undefined) {
        conditions.push('is_leaf = ?');
        params.push(query.is_leaf === 'true' ? 1 : 0);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const sql = `
        SELECT 
          id,
          platform_code,
          category_id,
          category_name,
          parent_category_id,
          level,
          is_leaf,
          created_at,
          updated_at
        FROM uploadproduct_categories 
        ${whereClause}
        ORDER BY level, category_name
      `;

      return await this.query(sql, params);
    } catch (error) {
      logger.error('Error getting categories:', error);
      throw error;
    }
  }

  // Get attributes
  public async getAttributes(query: any): Promise<any[]> {
    try {
      const conditions: string[] = [];
      const params: any[] = [];

      if (query.platform_code) {
        conditions.push('platform_code = ?');
        params.push(query.platform_code);
      }

      if (query.category_id) {
        conditions.push('category_id = ?');
        params.push(query.category_id);
      }

      if (query.translation_status) {
        conditions.push('translation_status = ?');
        params.push(query.translation_status);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const sql = `
        SELECT 
          id,
          platform_code,
          category_id,
          attribute_name,
          attribute_type,
          is_required,
          options,
          translation_status,
          created_at,
          updated_at
        FROM uploadproduct_category_attributes 
        ${whereClause}
        ORDER BY is_required DESC, attribute_name
      `;

      const results = await this.query(sql, params);
      
      // Parse JSON options field
      return results.map((attr: any) => ({
        ...attr,
        options: attr.options ? JSON.parse(attr.options) : []
      }));
    } catch (error) {
      logger.error('Error getting attributes:', error);
      throw error;
    }
  }
}

export const uploadproductModel = new UploadProductModel();
