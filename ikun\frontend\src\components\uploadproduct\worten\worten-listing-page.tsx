'use client'

import { useState, useEffect } from 'react'
import { useUploadProducts } from '@/hooks/useUploadProducts'
import { useStores } from '@/hooks/useStores'
import { useDropshipProducts } from '@/hooks/useDropshipProducts'
import { useConfirm } from '@/components/ui/confirm-dialog'
import { useToast } from '@/hooks/use-toast'
import { UploadProductListing, CreateUploadProductData } from '@/types'
import { UploadProductForm } from '@/components/uploadproduct/upload-product-form'
import { TranslationModal, LanguageCode } from '@/components/translation'
import { BatchTranslationProgress, useBatchTranslationProgress } from '@/components/ui/batch-translation-progress'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'

import {
  Plus,
  Upload,
  Edit,
  Copy,
  RefreshCw,
  ChevronDown,
  Trash2,
  Search,
  RotateCcw,
  MoreHorizontal,
  Languages,
  FileText,
  Download,
  Calendar,
  Store as StoreIcon,
  CheckCircle,
  Clock,
  AlertCircle,
  ExternalLink
} from 'lucide-react'

// 状态选项
const statusOptions = [
  { value: 'draft', label: '草稿', icon: FileText },
  { value: 'pending', label: '待上架', icon: Clock },
  { value: 'active', label: '已上架', icon: CheckCircle },
  { value: 'failed', label: '上架失败', icon: AlertCircle },
  { value: 'inactive', label: '已下架', icon: Trash2 },
]

// 翻译状态选项
const translationStatusOptions = [
  { value: 'pending', label: '待翻译', icon: Clock },
  { value: 'completed', label: '已完成', icon: CheckCircle },
]

interface UploadProductPageFullProps {
  platform: 'worten' | 'phh'
}

export function UploadProductPageFull({ platform }: UploadProductPageFullProps) {
  const [selectedProducts, setSelectedProducts] = useState<number[]>([])
  const [searchValue, setSearchValue] = useState('')
  const [selectedStore, setSelectedStore] = useState<string>('all')
  const [showForm, setShowForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<UploadProductListing | null>(null)
  const { confirm } = useConfirm()
  const { toast } = useToast()

  // 筛选状态 - 产品状态改为单选
  const [selectedStatus, setSelectedStatus] = useState<string>('') // 单选状态
  const [selectedTranslationStatus, setSelectedTranslationStatus] = useState<string>('') // 翻译状态也改为单选
  const [dateRange, setDateRange] = useState<{start: string, end: string}>({
    start: '',
    end: ''
  })

  // 翻译模态框状态
  const [translationModalOpen, setTranslationModalOpen] = useState(false)
  const [translationProduct, setTranslationProduct] = useState<UploadProductListing | null>(null)
  const [translationContentType, setTranslationContentType] = useState<'title' | 'description'>('title')
  const [refreshKey, setRefreshKey] = useState(0) // 强制刷新key

  // 批量翻译进度管理
  const batchTranslationProgress = useBatchTranslationProgress()

  // 使用API hooks
  const {
    uploadProducts,
    loading: productsLoading,
    error: productsError,
    pagination,
    fetchUploadProducts,
    createUploadProduct,
    updateUploadProduct,
    deleteUploadProduct
  } = useUploadProducts(platform);

  const {
    stores,
    loading: storesLoading,
    fetchStores
  } = useStores();

  const {
    dropshipProducts,
    loading: dropshipLoading,
    fetchDropshipProducts
  } = useDropshipProducts();

  // 获取当前平台的店铺
  const platformStores = stores.filter(store => store.platform_code === platform);

  // 筛选处理函数 - 改为单选模式
  const handleStatusSelect = (status: string) => {
    // 如果点击的是当前选中的状态，则切换为"全部"（空字符串）
    setSelectedStatus(prev => prev === status ? '' : status)
  }

  const handleTranslationStatusSelect = (status: string) => {
    // 如果点击的是当前选中的状态，则切换为"全部"（空字符串）
    setSelectedTranslationStatus(prev => prev === status ? '' : status)
  }

  // 日期格式化函数
  const formatDateDisplay = (dateStr: string) => {
    if (!dateStr) return ''
    // 如果是YYYY-MM-DD格式，转换为YYYY年MM月DD日显示
    if (dateStr.includes('-')) {
      const [year, month, day] = dateStr.split('-')
      if (year && month && day) {
        return `${year}年${month.padStart(2, '0')}月${day.padStart(2, '0')}日`
      }
    }
    return dateStr
  }

  // 处理日期输入
  const handleDateInput = (value: string, type: 'start' | 'end') => {
    // 只允许数字输入
    const numericValue = value.replace(/\D/g, '')

    if (numericValue.length <= 8) {
      let formattedValue = numericValue
      let displayValue = numericValue

      // 自动格式化显示
      if (numericValue.length >= 4) {
        const year = numericValue.slice(0, 4)
        displayValue = `${year}年`

        if (numericValue.length >= 6) {
          const month = numericValue.slice(4, 6)
          displayValue += `${month}月`

          if (numericValue.length === 8) {
            const day = numericValue.slice(6, 8)
            displayValue += `${day}日`
            // 转换为YYYY-MM-DD格式存储
            formattedValue = `${year}-${month}-${day}`
          }
        }
      }

      // 如果输入完整的8位数字，转换为标准日期格式
      if (numericValue.length === 8) {
        const year = numericValue.slice(0, 4)
        const month = numericValue.slice(4, 6)
        const day = numericValue.slice(6, 8)

        // 验证日期有效性
        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
        if (date.getFullYear() == parseInt(year) &&
            date.getMonth() == parseInt(month) - 1 &&
            date.getDate() == parseInt(day)) {
          formattedValue = `${year}-${month}-${day}`
        }
      }

      setDateRange(prev => ({
        ...prev,
        [type]: numericValue.length === 8 ? formattedValue : numericValue
      }))
    }
  }

  const handleReset = () => {
    setSelectedStatus('')
    setSelectedTranslationStatus('')
    setDateRange({ start: '', end: '' })
    setSearchValue('')
    setSelectedStore('all')
    // 重新获取所有产品
    fetchUploadProducts()
  }

  // 应用筛选
  const handleApplyFilters = () => {
    const params: any = {}

    if (searchValue) {
      params.search = searchValue
    }

    if (selectedStore && selectedStore !== 'all') {
      params.store_id = selectedStore
    }

    if (selectedStatus) {
      params.status = selectedStatus
    }

    if (selectedTranslationStatus) {
      params.translation_status = selectedTranslationStatus
    }

    if (dateRange.start) {
      params.start_date = dateRange.start
    }

    if (dateRange.end) {
      params.end_date = dateRange.end
    }

    fetchUploadProducts(params)
  }

  // 监听筛选条件变化，自动应用筛选
  useEffect(() => {
    const timer = setTimeout(() => {
      handleApplyFilters()
    }, 500) // 防抖处理

    return () => clearTimeout(timer)
  }, [searchValue, selectedStore, selectedStatus, selectedTranslationStatus, dateRange])

  // 表单处理函数
  const handleCreateProduct = async (data: CreateUploadProductData) => {
    await createUploadProduct(data)
    setShowForm(false)
  }

  // 编辑产品处理函数
  const handleUpdateProduct = async (data: any) => {
    if (!editingProduct) {
      toast({
        title: "错误",
        description: "编辑产品信息不存在",
        variant: "destructive"
      })
      return
    }

    try {
      await updateUploadProduct(editingProduct.id, data)
      toast({
        title: "成功",
        description: "产品更新成功"
      })
      // 刷新产品列表
      await fetchUploadProducts()
      setShowForm(false)
      setEditingProduct(null)
    } catch (error) {
      console.error('更新产品失败:', error)
      toast({
        title: "错误",
        description: "更新产品失败，请重试",
        variant: "destructive"
      })
      throw error
    }
  }

  // 根据模式选择处理函数
  const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct

  const handleEditProduct = (product: UploadProductListing) => {
    setEditingProduct(product)
    setShowForm(true)
  }

  const handleCloseForm = () => {
    setShowForm(false)
    setEditingProduct(null)
  }

  // 初始化数据
  useEffect(() => {
    fetchStores()
    fetchUploadProducts()
    fetchDropshipProducts()
  }, [platform])

  const handleDeleteProduct = async (productId: number) => {
    const confirmed = await confirm({
      title: '删除产品',
      description: '确定要删除这个上架产品吗？此操作不可撤销。',
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive'
    });

    if (!confirmed) {
      return;
    }

    try {
      await deleteUploadProduct(productId);
      setSelectedProducts(prev => prev.filter(id => id !== productId));
      toast({
        description: "产品已成功删除",
        variant: "default"
      });
    } catch (error) {
      console.error('删除产品失败:', error);
      toast({
        description: "删除产品时发生错误，请重试",
        variant: "destructive"
      });
    }
  }

  const handleBatchDelete = async () => {
    if (selectedProducts.length === 0) {
      await confirm({
        title: '提示',
        description: '请先选择要删除的产品',
        confirmText: '知道了',
        variant: 'info',
        icon: true
      });
      return;
    }

    const confirmed = await confirm({
      title: '批量删除',
      description: `确定要删除选中的 ${selectedProducts.length} 个产品吗？此操作不可撤销。`,
      confirmText: `删除 ${selectedProducts.length} 个产品`,
      cancelText: '取消',
      variant: 'destructive'
    });

    if (!confirmed) {
      return;
    }

    try {
      // 批量删除产品
      await Promise.all(selectedProducts.map(id => deleteUploadProduct(id)));
      setSelectedProducts([]);
      toast({
        description: `已成功删除 ${selectedProducts.length} 个产品`,
        variant: "default"
      });
    } catch (error) {
      console.error('批量删除失败:', error);
      toast({
        description: "删除产品时发生错误，请重试",
        variant: "destructive"
      });
    }
  }

  const handleBatchTranslation = async () => {
    if (selectedProducts.length === 0) {
      await confirm({
        title: '提示',
        description: '请先选择要翻译的产品',
        confirmText: '知道了',
        variant: 'info',
        icon: true
      });
      return;
    }

    const confirmed = await confirm({
      title: '批量翻译',
      description: `确定要翻译选中的 ${selectedProducts.length} 个产品吗？将翻译产品的标题、描述和卖点。`,
      confirmText: `翻译 ${selectedProducts.length} 个产品`,
      cancelText: '取消',
      variant: 'default'
    });

    if (!confirmed) {
      return;
    }

    // 准备进度数据
    const selectedProductsData = uploadProducts.filter(p => selectedProducts.includes(p.id))
    const progressItems = selectedProductsData.map(product => ({
      id: product.id,
      sku: product.sku,
      name: product.english_title || `产品 ${product.id}`
    }))

    // 启动进度对话框
    batchTranslationProgress.startTranslation(progressItems)

    try {
      // 获取认证 token
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('请先登录');
      }

      // 模拟逐个产品翻译进度（实际上后端是批量处理的）
      selectedProducts.forEach((productId, index) => {
        setTimeout(() => {
          batchTranslationProgress.setProcessingItem(productId)
        }, index * 100) // 每100ms标记一个产品为处理中
      })

      // 调用批量翻译 API
      const response = await fetch('/api/v1/translation/batch/forbatch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          platform: 'worten',
          source: 'form_batch', // 批量翻译场景
          sourceLang: 'en', // 默认从英语翻译
          targetLangs: getPlatformTargetLanguages('worten'), // 根据平台获取目标语言
          productids: selectedProducts.join(',')
        }),
      });

      if (!response.ok) {
        throw new Error('批量翻译请求失败');
      }

      const result = await response.json();

      // 检查响应状态 - 后端返回 code: 200 表示成功
      if (result.code === 200 && result.data?.success) {
        // 解析翻译结果
        const { results } = result.data;
        const successCount = results?.successful || 0;
        const failedCount = results?.failed || 0;
        const details = results?.details || [];

        // 更新每个产品的进度状态
        details.forEach((detail: any) => {
          if (detail.success) {
            batchTranslationProgress.setSuccessItem(detail.productId, '翻译完成')
          } else {
            batchTranslationProgress.setErrorItem(detail.productId, detail.error || '翻译失败')
          }
        })

        // 标记翻译完成
        batchTranslationProgress.finishTranslation()

        setSelectedProducts([]);

        if (successCount > 0) {
          toast({
            title: '批量翻译完成',
            description: `成功翻译 ${successCount} 个产品${failedCount > 0 ? `，失败 ${failedCount} 个` : ''}`,
            variant: "default"
          });
        } else {
          toast({
            title: '批量翻译失败',
            description: `所有产品翻译失败，请检查产品数据后重试`,
            variant: "destructive"
          });
        }

        // 强制刷新产品列表，确保显示最新的翻译状态
        console.log('批量翻译完成，开始刷新数据...');

        // 清除缓存并重新获取数据
        await fetchUploadProducts({
          _t: Date.now(),
          force_refresh: true
        });

        console.log('第一次刷新完成，当前组件状态中的产品数据:', uploadProducts.slice(0, 2).map(p => ({
          id: p.id,
          sku: p.sku,
          multi_titles: p.multi_titles,
          multi_descriptions: p.multi_descriptions
        })));

        console.log('刷新完成，refreshKey:', refreshKey);

        // 强制重新渲染组件
        setRefreshKey(prev => prev + 1);

        // 如果还是没有刷新，再次尝试
        setTimeout(async () => {
          console.log('延迟刷新开始...');
          await fetchUploadProducts({ _t: Date.now() });
          setRefreshKey(prev => prev + 1);
          console.log('延迟刷新完成');
        }, 1000);
      } else {
        throw new Error(result.data?.message || result.message || '批量翻译失败');
      }
    } catch (error) {
      console.error('批量翻译失败:', error);

      // 标记所有产品为失败
      selectedProducts.forEach(productId => {
        batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : '翻译失败')
      })

      // 标记翻译完成
      batchTranslationProgress.finishTranslation()

      toast({
        description: error instanceof Error ? error.message : "批量翻译时发生错误，请重试",
        variant: "destructive"
      });
    }
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      draft: { label: '草稿', className: 'bg-gray-100 text-gray-800' },
      pending: { label: '待上架', className: 'bg-yellow-100 text-yellow-800' },
      active: { label: '已上架', className: 'bg-green-100 text-green-800' },
      failed: { label: '上架失败', className: 'bg-red-100 text-red-800' },
      inactive: { label: '已下架', className: 'bg-gray-100 text-gray-800' }
    }
    const config = statusMap[status as keyof typeof statusMap] || statusMap.draft
    return (
      <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const getTranslationStatusBadge = (status: string) => {
    const statusMap = {
      pending: { label: '待翻译', className: 'bg-orange-100 text-orange-800' },
      completed: { label: '已完成', className: 'bg-green-100 text-green-800' }
    }
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
        {config.label}
      </span>
    )
  }

  // 获取平台显示名称
  const getPlatformName = (platformCode: string) => {
    const platformMap: Record<string, string> = {
      worten: 'Worten',
      phh: 'PHH',
      amazon: 'Amazon',
      ebay: 'eBay',
      shopify: 'Shopify'
    }
    return platformMap[platformCode] || platformCode.toUpperCase()
  }

  // 获取平台所需的语言列表
  const getPlatformRequiredLanguages = (platformCode: string): string[] => {
    const platformLanguages: Record<string, string[]> = {
      worten: ['PT', 'ES'], // Worten需要葡萄牙语和西班牙语
      phh: ['LT', 'LV', 'EE', 'FI'], // PHH需要立陶宛语、拉脱维亚语、爱沙尼亚语、芬兰语
      amazon: ['EN', 'DE', 'FR', 'IT', 'ES'],
      ebay: ['EN'],
      shopify: ['EN']
    }
    return platformLanguages[platformCode] || ['EN']
  }

  // 获取平台翻译目标语言（转换为LanguageCode格式）
  const getPlatformTargetLanguages = (platformCode: string): LanguageCode[] => {
    const languageMap: Record<string, LanguageCode> = {
      'PT': 'pt',
      'ES': 'es',
      'LT': 'lt',
      'LV': 'lv',
      'EE': 'et', // 爱沙尼亚语
      'FI': 'fi',
      'EN': 'en',
      'DE': 'zh', // 暂时映射到中文，实际项目中需要支持德语
      'FR': 'zh', // 暂时映射到中文，实际项目中需要支持法语
      'IT': 'zh'  // 暂时映射到中文，实际项目中需要支持意大利语
    }

    const platformLanguages = getPlatformRequiredLanguages(platformCode)
    return platformLanguages.map(lang => languageMap[lang] || 'en' as LanguageCode)
  }

  // 单个产品翻译处理
  const handleProductTranslation = (product: UploadProductListing, contentType: 'title' | 'description') => {
    setTranslationProduct(product)
    setTranslationContentType(contentType)
    setTranslationModalOpen(true)
  }

  // 批量翻译功能已移除，使用单个产品翻译

  // 翻译完成处理
  const handleTranslationComplete = async (translations: Record<string, string>) => {
    if (!translationProduct) return

    try {
      // 构建更新数据
      const updateData: any = {}

      if (translationContentType === 'title') {
        updateData.multi_titles = {
          ...(translationProduct.multi_titles || {}),
          ...translations
        }
      } else {
        updateData.multi_descriptions = {
          ...(translationProduct.multi_descriptions || {}),
          ...translations
        }
      }

      // 更新翻译状态
      updateData.listings_translation_status = 'completed'

      // 调用更新API
      await updateUploadProduct(translationProduct.id, updateData)

      toast({
        title: '翻译完成',
        description: `${translationContentType === 'title' ? '标题' : '描述'}翻译已保存`
      })

      // 刷新数据
      await fetchUploadProducts()

    } catch (error) {
      console.error('Translation save error:', error)
      toast({
        title: '保存失败',
        description: '翻译结果保存失败，请重试',
        variant: 'destructive'
      })
    }
  }

  // 渲染多语言状态组件 - 竖着排列，紧凑样式
  const renderMultiLanguageStatus = (multiLangData: Record<string, string> | null, requiredLanguages: string[]) => {
    const data = multiLangData || {}

    return (
      <div className="flex flex-col items-center space-y-1">
        {requiredLanguages.map(lang => {
          const hasTranslation = data[lang] && data[lang].trim() !== ''
          return (
            <div
              key={lang}
              className={`px-1 py-0.5 rounded text-xs font-medium text-center w-8 ${
                hasTranslation
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}
            >
              {lang}
            </div>
          )
        })}
      </div>
    )
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(uploadProducts.map((p: UploadProductListing) => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  const handleSelectProduct = (productId: number, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId])
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId))
    }
  }

  // 处理产品行单击选中
  const handleProductClick = (productId: number, event: React.MouseEvent) => {
    // 防止复选框点击触发行点击
    if ((event.target as HTMLElement).closest('input[type="checkbox"]') ||
        (event.target as HTMLElement).closest('button')) {
      return
    }

    const isSelected = selectedProducts.includes(productId)
    handleSelectProduct(productId, !isSelected)
  }

  // 显示加载状态
  if (productsLoading && uploadProducts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (productsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">加载失败: {productsError}</p>
          <Button onClick={() => fetchUploadProducts()} variant="outline">
            重试
          </Button>
        </div>
      </div>
    );
  }

  const platformName = getPlatformName(platform);

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)]">
      <Card className="flex-1 flex flex-col">
        <CardContent className="p-0 flex-1 flex flex-col">
          {/* 工具栏 */}
          <div className="border-b p-4">
            {/* 主要操作按钮 */}
            <div className="flex flex-wrap gap-2 mb-4">
              <Button size="sm" variant="outline" onClick={() => setShowForm(true)}>
                <Plus className="w-4 h-4 mr-1" />
                新增上架
              </Button>
              <Button size="sm" variant="outline">
                <Upload className="w-4 h-4 mr-1" />
                批量上架
              </Button>
              {/* 批量翻译功能已移除，使用单个产品的翻译按钮 */}
              <Button size="sm" variant="outline">
                <RefreshCw className="w-4 h-4 mr-1" />
                同步状态
              </Button>
              
              {/* 批量操作下拉菜单 */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" variant="outline">
                    批量操作
                    <ChevronDown className="w-4 h-4 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuItem onClick={handleBatchTranslation}>
                    <Languages className="w-4 h-4 mr-2" />
                    批量翻译
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleBatchDelete}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    批量删除
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="w-4 h-4 mr-2" />
                    批量导出
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* 筛选和搜索区域 */}
            <div className="pt-4 border-t">
              <div className="flex items-center justify-between gap-4">
                {/* 左侧：筛选菜单 */}
                <div className="flex items-center gap-3">
                  {/* 店铺筛选 */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="w-32 justify-start">
                        <StoreIcon className="w-4 h-4 mr-1 flex-shrink-0" />
                        <span className="truncate">店铺</span>
                        {selectedStore !== 'all' && (
                          <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0">
                            1
                          </span>
                        )}
                        <ChevronDown className="w-4 h-4 ml-auto flex-shrink-0" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => setSelectedStore('all')}>
                        全部店铺
                      </DropdownMenuItem>
                      {platformStores.map((store) => (
                        <DropdownMenuItem
                          key={store.id}
                          onClick={() => setSelectedStore(store.id.toString())}
                        >
                          {store.store_name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* 状态标签按钮 */}
                  <div className="flex items-center gap-2">
                    {/* 全部状态按钮 */}
                    <Button
                      size="sm"
                      variant={selectedStatus === '' ? "default" : "outline"}
                      onClick={() => setSelectedStatus('')}
                      className="h-8"
                    >
                      全部
                    </Button>

                    {/* 状态标签按钮 */}
                    {statusOptions.map((option) => {
                      const isSelected = selectedStatus === option.value
                      return (
                        <Button
                          key={option.value}
                          size="sm"
                          variant={isSelected ? "default" : "outline"}
                          onClick={() => handleStatusSelect(option.value)}
                          className="h-8"
                        >
                          <option.icon className="w-3 h-3 mr-1" />
                          {option.label}
                        </Button>
                      )
                    })}
                  </div>

                  {/* 翻译状态标签按钮 */}
                  <div className="flex items-center gap-2">
                    {/* 翻译状态标签按钮 */}
                    {translationStatusOptions.map((option) => {
                      const isSelected = selectedTranslationStatus === option.value
                      return (
                        <Button
                          key={option.value}
                          size="sm"
                          variant={isSelected ? "default" : "outline"}
                          onClick={() => handleTranslationStatusSelect(option.value)}
                          className="h-8"
                        >
                          <option.icon className="w-3 h-3 mr-1" />
                          翻译-{option.label}
                        </Button>
                      )
                    })}
                  </div>

                  {/* 创建时间筛选 */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="w-32 justify-start">
                        <Calendar className="w-4 h-4 mr-1 flex-shrink-0" />
                        <span className="truncate">创建时间</span>
                        {(dateRange.start || dateRange.end) && (
                          <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0">
                            1
                          </span>
                        )}
                        <ChevronDown className="w-4 h-4 ml-auto flex-shrink-0" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-64 p-3">
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium">开始日期</label>
                          <Input
                            placeholder="年/月/日"
                            value={formatDateDisplay(dateRange.start)}
                            onChange={(e) => handleDateInput(e.target.value, 'start')}
                            className="mt-1"
                            maxLength={10}
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">结束日期</label>
                          <Input
                            placeholder="年/月/日"
                            value={formatDateDisplay(dateRange.end)}
                            onChange={(e) => handleDateInput(e.target.value, 'end')}
                            className="mt-1"
                            maxLength={10}
                          />
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* 右侧：搜索区域 */}
                <div className="flex items-center gap-2">
                  <Input
                    placeholder="搜索SKU、EAN、标题..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    className="w-64"
                  />
                  <Button size="sm">
                    <Search className="w-4 h-4 mr-1" />
                    搜索
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleReset}>
                    <RotateCcw className="w-4 h-4 mr-1" />
                    重置
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {uploadProducts.length === 0 ? (
            <div className="flex-1 flex flex-col items-center justify-center text-center p-8">
              <h3 className="text-lg font-medium mb-2">暂无产品</h3>
            </div>
          ) : (
            <div className="flex-1 overflow-x-auto">
                <table className="w-full text-sm border-separate border-spacing-0 table-fixed">
                  <colgroup>
                    <col className="w-12" />
                    <col className="w-16" />
                    <col className="w-[35%]" />
                    <col className="w-20" />
                    <col className="w-[10%]" />
                    <col className="w-24" />
                    <col className="w-28" />
                    <col className="w-24" />
                    <col className="w-32" />
                    <col className="w-32" />
                    <col className="w-16" />
                  </colgroup>
                  <thead>
                    <tr className="bg-muted/30 border-b h-14">
                      <th className="p-3 text-left border-r border-border/50 h-14">
                        <div className="flex items-center justify-center h-full">
                          <Checkbox
                            checked={selectedProducts.length === uploadProducts.length && uploadProducts.length > 0}
                            onCheckedChange={handleSelectAll}
                          />
                        </div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">图片</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">标题/OfferID/店铺/分类</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14">
                        <div className="flex items-center justify-center h-full">状态</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">SKU/EAN</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">库存</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">售价（€）</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">多语言标题</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">多语言描述</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                        <div className="flex items-center justify-center h-full">创建时间/发布时间</div>
                      </th>
                      <th className="p-3 text-left font-medium text-muted-foreground h-14">
                        <div className="flex items-center justify-center h-full">操作</div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {uploadProducts.map((product: UploadProductListing, index) => (
                      <tr
                        key={`${product.id}-${refreshKey}`}
                        className={`border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 ${
                          selectedProducts.includes(product.id)
                            ? 'bg-blue-50 border-blue-200'
                            : index % 2 === 0 ? 'bg-background' : 'bg-muted/10'
                        }`}
                        onClick={(e) => handleProductClick(product.id, e)}
                      >
                        <td className="p-3 border-r border-border/50">
                          <Checkbox
                            checked={selectedProducts.includes(product.id)}
                            onCheckedChange={(checked) => handleSelectProduct(product.id, checked as boolean)}
                          />
                        </td>
                        <td className="p-3 border-r border-border/50">
                          <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm">
                            {product.image1 ? (
                              <img
                                src={product.image1}
                                alt={product.english_title || ''}
                                className="w-full h-full object-cover cursor-pointer"
                              />
                            ) : (
                              <div className="text-muted-foreground text-xs">无图片</div>
                            )}
                          </div>
                        </td>
                        <td className="p-3 border-r border-border/50">
                          <div className="space-y-1">
                            <div className="font-medium text-foreground leading-tight line-clamp-1" title={product.english_title || '未设置标题'}>
                              {product.english_title || '未设置标题'}
                            </div>
                            <div className="text-xs text-muted-foreground line-clamp-1">
                              ID: {product.id} | 店铺: {stores.find(s => s.id === product.store_id)?.store_name || '未知店铺'}
                            </div>
                            <div className="text-muted-foreground text-xs line-clamp-1">
                              分类: {product.platform_category_id || '未设置'}
                            </div>
                          </div>
                        </td>
                        <td className="p-3 border-r border-border/50 text-center">
                          {getStatusBadge(product.status)}
                        </td>
                        <td className="p-3 border-r border-border/50 text-center">
                          <div className="text-sm space-y-1">
                            <div className="font-medium text-foreground leading-tight line-clamp-1">{product.upstores_sku || product.sku}</div>
                            <div className="font-medium text-foreground leading-tight line-clamp-1">{product.upstores_ean || product.ean}</div>
                          </div>
                        </td>
                        <td className="p-3 border-r border-border/50 text-center">
                          <div className="font-semibold text-blue-600 text-sm">{product.stock_quantity || 0}</div>
                        </td>
                        <td className="p-3 border-r border-border/50 text-center">
                          <div className="font-semibold text-green-600 text-sm">
                            {product.discounted_price ? `€${product.discounted_price}` : '未设置'}
                          </div>
                        </td>
                        <td className="p-3 border-r border-border/50 text-center">
                          {renderMultiLanguageStatus(
                            product.multi_titles || null,
                            getPlatformRequiredLanguages(platform)
                          )}
                        </td>
                        <td className="p-3 border-r border-border/50 text-center">
                          {renderMultiLanguageStatus(
                            product.multi_descriptions || null,
                            getPlatformRequiredLanguages(platform)
                          )}
                        </td>
                        <td className="p-3 border-r border-border/50">
                          <div className="text-xs text-muted-foreground">
                            {new Date(product.uplisting_at).toLocaleString('zh-CN')}
                          </div>
                        </td>
                        <td className="p-3">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditProduct(product)}>
                                <Edit className="w-4 h-4 mr-2" />
                                编辑产品
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Copy className="w-4 h-4 mr-2" />
                                复制到其他店铺
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {product.status === 'draft' && (
                                <DropdownMenuItem>
                                  <Upload className="w-4 h-4 mr-2" />
                                  提交上架
                                </DropdownMenuItem>
                              )}
                              {product.status === 'active' && (
                                <DropdownMenuItem>
                                  <RefreshCw className="w-4 h-4 mr-2" />
                                  同步状态
                                </DropdownMenuItem>
                              )}
                              {product.listings_translation_status === 'pending' && (
                                <>
                                  <DropdownMenuItem onClick={() => handleProductTranslation(product, 'title')}>
                                    <Languages className="w-4 h-4 mr-2" />
                                    翻译标题
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleProductTranslation(product, 'description')}>
                                    <FileText className="w-4 h-4 mr-2" />
                                    翻译描述
                                  </DropdownMenuItem>
                                </>
                              )}
                              <DropdownMenuItem>
                                <ExternalLink className="w-4 h-4 mr-2" />
                                查看原产品
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteProduct(product.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                删除刊登
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

          {/* 固定底部分页区域 */}
          <div className="flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto">
            <div className="text-sm text-muted-foreground">
              显示 {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条记录
            </div>
            <div className="flex items-center space-x-1">
              <Button
                size="sm"
                variant="outline"
                disabled={pagination.page <= 1}
                onClick={() => fetchUploadProducts({ page: pagination.page - 1 })}
                className="h-8 px-3 text-xs"
              >
                上一页
              </Button>

              {/* 页码按钮 */}
              {Array.from({ length: Math.min(5, Math.ceil(pagination.total / pagination.limit)) }, (_, i) => {
                const pageNum = Math.max(1, pagination.page - 2) + i;
                if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;

                return (
                  <Button
                    key={pageNum}
                    size="sm"
                    variant={pageNum === pagination.page ? "default" : "outline"}
                    onClick={() => fetchUploadProducts({ page: pageNum })}
                    className="h-8 w-8 p-0 text-xs"
                  >
                    {pageNum}
                  </Button>
                );
              })}

              <Button
                size="sm"
                variant="outline"
                disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
                onClick={() => fetchUploadProducts({ page: pagination.page + 1 })}
                className="h-8 px-3 text-xs"
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 产品上架表单 */}
      <UploadProductForm
        open={showForm}
        onClose={handleCloseForm}
        onSubmit={handleFormSubmit}
        platform={platform}
        stores={stores}
        editingProduct={editingProduct}
        mode={editingProduct ? 'edit' : 'add'}
      />

      {/* 单个产品翻译模态框 */}
      {translationProduct && (
        <TranslationModal
          open={translationModalOpen}
          onOpenChange={setTranslationModalOpen}
          title={`翻译${translationContentType === 'title' ? '标题' : '描述'} - ${translationProduct.sku}`}
          initialText={
            translationContentType === 'title'
              ? translationProduct.english_title || ''
              : translationProduct.multi_descriptions?.EN || ''
          }
          sourceLang="en"
          targetLangs={getPlatformTargetLanguages(platform)}
          contentType={translationContentType}
          platform={platform} // 传入平台参数
          source="form_batch" // 批量翻译场景
          onTranslationComplete={handleTranslationComplete}
          onTranslationError={(errors) => {
            console.error('Translation errors:', errors)
            toast({
              title: '翻译失败',
              description: '部分语言翻译失败，请查看详情',
              variant: 'destructive'
            })
          }}
        />
      )}

      {/* 批量翻译进度对话框 */}
      <BatchTranslationProgress
        open={batchTranslationProgress.isOpen}
        onOpenChange={batchTranslationProgress.setIsOpen}
        items={batchTranslationProgress.items}
        isProcessing={batchTranslationProgress.isProcessing}
        onCancel={batchTranslationProgress.cancelTranslation}
        onComplete={async () => {
          // 翻译完成后刷新数据
          await fetchUploadProducts({ _t: Date.now() })
          setRefreshKey(prev => prev + 1)
          // 延迟关闭对话框，让用户看到完成状态
          setTimeout(() => {
            batchTranslationProgress.closeDialog()
          }, 2000)
        }}
      />
    </div>
  )
}
