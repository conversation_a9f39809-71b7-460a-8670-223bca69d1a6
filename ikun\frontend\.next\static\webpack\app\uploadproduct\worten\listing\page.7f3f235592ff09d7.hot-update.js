"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态 - 产品状态改为单选\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 单选状态\n    ;\n    const [selectedTranslationStatus, setSelectedTranslationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 翻译状态也改为单选\n    ;\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数 - 改为单选模式\n    const handleStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleTranslationStatusSelect = (status)=>{\n        // 如果点击的是当前选中的状态，则切换为\"全部\"（空字符串）\n        setSelectedTranslationStatus((prev)=>prev === status ? \"\" : status);\n    };\n    const handleReset = ()=>{\n        setSelectedStatus(\"\");\n        setSelectedTranslationStatus(\"\");\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatus) {\n            params.status = selectedStatus;\n        }\n        if (selectedTranslationStatus) {\n            params.translation_status = selectedTranslationStatus;\n        }\n        if (dateRange.start) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end) {\n            params.end_date = dateRange.end;\n        }\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatus,\n        selectedTranslationStatus,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 474,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 487,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 594,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 646,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 645,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 658,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 657,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: selectedStatus === \"\" ? \"default\" : \"outline\",\n                                                                onClick: ()=>setSelectedStatus(\"\"),\n                                                                className: \"h-8\",\n                                                                children: \"全部\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            statusOptions.map((option)=>{\n                                                                const isSelected = selectedStatus === option.value;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: isSelected ? \"default\" : \"outline\",\n                                                                    onClick: ()=>handleStatusSelect(option.value),\n                                                                    className: \"h-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        option.label\n                                                                    ]\n                                                                }, option.value, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: translationStatusOptions.map((option)=>{\n                                                            const isSelected = selectedTranslationStatus === option.value;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                size: \"sm\",\n                                                                variant: isSelected ? \"default\" : \"outline\",\n                                                                onClick: ()=>handleTranslationStatusSelect(option.value),\n                                                                className: \"h-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"翻译-\",\n                                                                    option.label\n                                                                ]\n                                                            }, option.value, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-64 p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.start,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                start: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 820,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 818,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 828,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.end,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                end: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 854,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 879,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 956,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 962,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 990,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 997,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1005,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1004,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1003,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1010,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1009,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1014,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1013,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1017,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1020,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1019,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1026,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1025,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1033,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1032,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1037,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1036,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1043,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1042,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1046,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1051,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1008,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1002,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 868,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1066,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1070,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1098,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1069,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1065,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 673,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 672,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1113,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1125,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.BatchTranslationProgress, {\n                open: batchTranslationProgress.isOpen,\n                onOpenChange: batchTranslationProgress.setIsOpen,\n                items: batchTranslationProgress.items,\n                isProcessing: batchTranslationProgress.isProcessing,\n                onCancel: batchTranslationProgress.cancelTranslation,\n                onComplete: async ()=>{\n                    // 翻译完成后刷新数据\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    // 延迟关闭对话框，让用户看到完成状态\n                    setTimeout(()=>{\n                        batchTranslationProgress.closeDialog();\n                    }, 2000);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 671,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"dWHnEtktIVXSiFxIRpDIyJSCiHE=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});