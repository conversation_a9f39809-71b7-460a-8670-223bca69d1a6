"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx":
/*!**********************************************************!*\
  !*** ./src/components/ui/batch-translation-progress.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BatchTranslationProgress: function() { return /* binding */ BatchTranslationProgress; },\n/* harmony export */   useBatchTranslationProgress: function() { return /* binding */ useBatchTranslationProgress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _progress_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./progress-dialog */ \"(app-pages-browser)/./src/components/ui/progress-dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ BatchTranslationProgress,useBatchTranslationProgress auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction BatchTranslationProgress(param) {\n    let { open, onOpenChange, items, isProcessing, onCancel, onComplete } = param;\n    _s();\n    const [progress, setProgress] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    // 转换为通用进度项格式\n    const progressItems = items.map((item)=>({\n            id: item.productId,\n            name: item.sku,\n            status: item.status,\n            message: item.message,\n            error: item.error\n        }));\n    // 计算进度\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const completedCount = items.filter((item)=>item.status === \"success\" || item.status === \"error\").length;\n        const newProgress = items.length > 0 ? completedCount / items.length * 100 : 0;\n        setProgress(newProgress);\n        // 如果全部完成且不在处理中，触发完成回调\n        if (completedCount === items.length && items.length > 0 && !isProcessing) {\n            setTimeout(()=>{\n                onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n            }, 1000) // 延迟1秒让用户看到完成状态\n            ;\n        }\n    }, [\n        items,\n        isProcessing,\n        onComplete\n    ]);\n    const successCount = items.filter((item)=>item.status === \"success\").length;\n    const errorCount = items.filter((item)=>item.status === \"error\").length;\n    const getSuccessMessage = ()=>{\n        if (successCount === items.length) {\n            return \"所有 \".concat(items.length, \" 个产品翻译完成！\");\n        }\n        if (successCount > 0) {\n            return \"成功翻译 \".concat(successCount, \" 个产品\");\n        }\n        return undefined;\n    };\n    const getErrorMessage = ()=>{\n        if (errorCount === items.length) {\n            return \"所有 \".concat(items.length, \" 个产品翻译失败，请检查后重试\");\n        }\n        if (errorCount > 0) {\n            return \"\".concat(errorCount, \" 个产品翻译失败\");\n        }\n        return undefined;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_progress_dialog__WEBPACK_IMPORTED_MODULE_2__.ProgressDialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        title: \"批量翻译进度\",\n        items: progressItems,\n        progress: progress,\n        isProcessing: isProcessing,\n        canCancel: true,\n        onCancel: onCancel,\n        showDetails: true,\n        successMessage: getSuccessMessage(),\n        errorMessage: getErrorMessage()\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\batch-translation-progress.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(BatchTranslationProgress, \"ZVQpwjU6Dz5R8VBOzPsnxGRmMVo=\");\n_c = BatchTranslationProgress;\n// Hook for managing batch translation progress\nfunction useBatchTranslationProgress() {\n    _s1();\n    const [items, setItems] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const [isProcessing, setIsProcessing] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const startTranslation = (productList)=>{\n        const initialItems = productList.map((product)=>({\n                productId: product.id,\n                sku: product.sku,\n                name: product.name,\n                status: \"pending\",\n                message: \"等待翻译...\"\n            }));\n        setItems(initialItems);\n        setIsProcessing(true);\n        setIsOpen(true);\n    };\n    const updateItemStatus = (productId, status, message, error)=>{\n        setItems((prev)=>prev.map((item)=>item.productId === productId ? {\n                    ...item,\n                    status,\n                    message,\n                    error\n                } : item));\n    };\n    const setProcessingItem = (productId)=>{\n        updateItemStatus(productId, \"processing\", \"正在翻译...\");\n    };\n    const setSuccessItem = (productId, message)=>{\n        updateItemStatus(productId, \"success\", message || \"翻译完成\");\n    };\n    const setErrorItem = (productId, error)=>{\n        updateItemStatus(productId, \"error\", undefined, error);\n    };\n    const finishTranslation = ()=>{\n        setIsProcessing(false);\n    };\n    const cancelTranslation = ()=>{\n        setIsProcessing(false);\n        setIsOpen(false);\n        setItems([]);\n    };\n    const closeDialog = ()=>{\n        setIsOpen(false);\n        setItems([]);\n    };\n    return {\n        items,\n        isProcessing,\n        isOpen,\n        startTranslation,\n        setProcessingItem,\n        setSuccessItem,\n        setErrorItem,\n        finishTranslation,\n        cancelTranslation,\n        closeDialog,\n        setIsOpen\n    };\n}\n_s1(useBatchTranslationProgress, \"q71jHa2BGVvl/cXyQZQ9LY8YO6M=\");\nvar _c;\n$RefreshReg$(_c, \"BatchTranslationProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\n"));

/***/ })

});