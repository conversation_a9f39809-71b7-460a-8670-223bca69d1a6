"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CalendarProps {
  selected?: Date
  onSelect?: (date: Date | undefined) => void
  dateRange?: { start: Date | undefined, end: Date | undefined }
  onDateRangeChange?: (range: { start: Date | undefined, end: Date | undefined }) => void
  mode?: 'single' | 'range'
  className?: string
}

const MONTHS = [
  "1月", "2月", "3月", "4月", "5月", "6月",
  "7月", "8月", "9月", "10月", "11月", "12月"
]

const WEEKDAYS = ["日", "一", "二", "三", "四", "五", "六"]

export function Calendar({
  selected,
  onSelect,
  dateRange,
  onDateRangeChange,
  mode = 'single',
  className
}: CalendarProps) {
  const [currentDate, setCurrentDate] = React.useState(selected || dateRange?.start || new Date())
  const [rangeStart, setRangeStart] = React.useState<Date | undefined>(dateRange?.start)
  const [rangeEnd, setRangeEnd] = React.useState<Date | undefined>(dateRange?.end)
  
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  
  // 获取当月第一天是星期几
  const firstDayOfMonth = new Date(year, month, 1).getDay()
  
  // 获取当月天数
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  
  // 获取上月天数
  const daysInPrevMonth = new Date(year, month, 0).getDate()
  
  // 生成日历数组
  const calendarDays = []
  
  // 上月的日期
  for (let i = firstDayOfMonth - 1; i >= 0; i--) {
    calendarDays.push({
      day: daysInPrevMonth - i,
      isCurrentMonth: false,
      isPrevMonth: true,
      date: new Date(year, month - 1, daysInPrevMonth - i)
    })
  }
  
  // 当月的日期
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: true,
      isPrevMonth: false,
      date: new Date(year, month, day)
    })
  }
  
  // 下月的日期（补齐42个格子）
  const remainingDays = 42 - calendarDays.length
  for (let day = 1; day <= remainingDays; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: false,
      isPrevMonth: false,
      date: new Date(year, month + 1, day)
    })
  }
  
  const goToPrevMonth = () => {
    setCurrentDate(new Date(year, month - 1, 1))
  }
  
  const goToNextMonth = () => {
    setCurrentDate(new Date(year, month + 1, 1))
  }
  
  const goToToday = () => {
    const today = new Date()
    setCurrentDate(today)
    onSelect?.(today)
  }
  
  const handleDateClick = (date: Date) => {
    if (mode === 'single') {
      onSelect?.(date)
    } else if (mode === 'range') {
      if (!rangeStart || (rangeStart && rangeEnd)) {
        // 开始新的选择
        setRangeStart(date)
        setRangeEnd(undefined)
        onDateRangeChange?.({ start: date, end: undefined })
      } else if (rangeStart && !rangeEnd) {
        // 选择结束日期
        if (date >= rangeStart) {
          setRangeEnd(date)
          onDateRangeChange?.({ start: rangeStart, end: date })
        } else {
          // 如果选择的日期早于开始日期，则重新开始
          setRangeStart(date)
          setRangeEnd(undefined)
          onDateRangeChange?.({ start: date, end: undefined })
        }
      }
    }
  }

  const clearSelection = () => {
    if (mode === 'single') {
      onSelect?.(undefined)
    } else {
      setRangeStart(undefined)
      setRangeEnd(undefined)
      onDateRangeChange?.({ start: undefined, end: undefined })
    }
  }

  const goToToday = () => {
    const today = new Date()
    setCurrentDate(today)
    if (mode === 'single') {
      onSelect?.(today)
    } else {
      setRangeStart(today)
      setRangeEnd(undefined)
      onDateRangeChange?.({ start: today, end: undefined })
    }
  }

  const confirmSelection = () => {
    // 确认选择后可以关闭弹窗，这里暂时不做处理
    // 实际使用时可以通过props传入onConfirm回调
  }
  
  const isSelected = (date: Date) => {
    if (mode === 'single') {
      if (!selected) return false
      return date.toDateString() === selected.toDateString()
    } else {
      if (!rangeStart) return false
      if (rangeEnd) {
        return date >= rangeStart && date <= rangeEnd
      } else {
        return date.toDateString() === rangeStart.toDateString()
      }
    }
  }

  const isRangeStart = (date: Date) => {
    return mode === 'range' && rangeStart && date.toDateString() === rangeStart.toDateString()
  }

  const isRangeEnd = (date: Date) => {
    return mode === 'range' && rangeEnd && date.toDateString() === rangeEnd.toDateString()
  }

  const isInRange = (date: Date) => {
    return mode === 'range' && rangeStart && rangeEnd && date > rangeStart && date < rangeEnd
  }

  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }
  
  return (
    <div className={cn("p-3 w-80 bg-white border rounded-lg shadow-sm", className)}>
      {/* 头部 - 年月导航 */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={goToPrevMonth}
          className="h-7 w-7 p-0 hover:bg-gray-100"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="font-medium text-sm text-gray-900">
          {year}年 {MONTHS[month]}
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={goToNextMonth}
          className="h-7 w-7 p-0 hover:bg-gray-100"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      
      {/* 星期标题 */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {WEEKDAYS.map((weekday) => (
          <div
            key={weekday}
            className="h-8 flex items-center justify-center text-xs font-medium text-gray-500"
          >
            {weekday}
          </div>
        ))}
      </div>

      {/* 日期网格 */}
      <div className="grid grid-cols-7 gap-1 mb-4">
        {calendarDays.map((dayInfo, index) => {
          const isSelectedDate = isSelected(dayInfo.date)
          const isTodayDate = isToday(dayInfo.date)
          const isStartDate = isRangeStart(dayInfo.date)
          const isEndDate = isRangeEnd(dayInfo.date)
          const isInRangeDate = isInRange(dayInfo.date)

          return (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              onClick={() => handleDateClick(dayInfo.date)}
              className={cn(
                "h-8 w-8 p-0 text-xs font-normal hover:bg-gray-100 rounded",
                !dayInfo.isCurrentMonth && "text-gray-400 opacity-60",
                // 单选模式的选中样式
                mode === 'single' && isSelectedDate && "bg-teal-500 text-white hover:bg-teal-600",
                // 区间模式的样式
                mode === 'range' && (isStartDate || isEndDate) && "bg-teal-500 text-white hover:bg-teal-600",
                mode === 'range' && isInRangeDate && "bg-teal-100 text-teal-800 hover:bg-teal-200",
                // 今天的样式（未选中时）
                isTodayDate && !isSelectedDate && !isStartDate && !isEndDate && "bg-blue-50 text-blue-600 font-medium"
              )}
            >
              {dayInfo.day}
            </Button>
          )
        })}
      </div>
      
      {/* 底部按钮 */}
      <div className="flex items-center justify-end gap-2 pt-2 border-t border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={clearSelection}
          className="h-7 px-3 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100"
        >
          清空
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={goToToday}
          className="h-7 px-3 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100"
        >
          现在
        </Button>
        <Button
          size="sm"
          onClick={confirmSelection}
          className="h-7 px-3 text-xs bg-blue-500 hover:bg-blue-600 text-white"
        >
          确定
        </Button>
      </div>
    </div>
  )
}
