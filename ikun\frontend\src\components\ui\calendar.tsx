"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CalendarProps {
  selected?: Date
  onSelect?: (date: Date | undefined) => void
  className?: string
}

const MONTHS = [
  "1月", "2月", "3月", "4月", "5月", "6月",
  "7月", "8月", "9月", "10月", "11月", "12月"
]

const WEEKDAYS = ["日", "一", "二", "三", "四", "五", "六"]

export function Calendar({ selected, onSelect, className }: CalendarProps) {
  const [currentDate, setCurrentDate] = React.useState(selected || new Date())
  
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  
  // 获取当月第一天是星期几
  const firstDayOfMonth = new Date(year, month, 1).getDay()
  
  // 获取当月天数
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  
  // 获取上月天数
  const daysInPrevMonth = new Date(year, month, 0).getDate()
  
  // 生成日历数组
  const calendarDays = []
  
  // 上月的日期
  for (let i = firstDayOfMonth - 1; i >= 0; i--) {
    calendarDays.push({
      day: daysInPrevMonth - i,
      isCurrentMonth: false,
      isPrevMonth: true,
      date: new Date(year, month - 1, daysInPrevMonth - i)
    })
  }
  
  // 当月的日期
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: true,
      isPrevMonth: false,
      date: new Date(year, month, day)
    })
  }
  
  // 下月的日期（补齐42个格子）
  const remainingDays = 42 - calendarDays.length
  for (let day = 1; day <= remainingDays; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: false,
      isPrevMonth: false,
      date: new Date(year, month + 1, day)
    })
  }
  
  const goToPrevMonth = () => {
    setCurrentDate(new Date(year, month - 1, 1))
  }
  
  const goToNextMonth = () => {
    setCurrentDate(new Date(year, month + 1, 1))
  }
  
  const goToToday = () => {
    const today = new Date()
    setCurrentDate(today)
    onSelect?.(today)
  }
  
  const clearSelection = () => {
    onSelect?.(undefined)
  }
  
  const confirmSelection = () => {
    // 确认选择后可以关闭弹窗，这里暂时不做处理
    // 实际使用时可以通过props传入onConfirm回调
  }
  
  const isSelected = (date: Date) => {
    if (!selected) return false
    return date.toDateString() === selected.toDateString()
  }
  
  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }
  
  return (
    <div className={cn("p-3 w-80 bg-white border rounded-lg shadow-sm", className)}>
      {/* 头部 - 年月导航 */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={goToPrevMonth}
          className="h-7 w-7 p-0 hover:bg-gray-100"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="font-medium text-sm text-gray-900">
          {year}年 {MONTHS[month]}
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={goToNextMonth}
          className="h-7 w-7 p-0 hover:bg-gray-100"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      
      {/* 星期标题 */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {WEEKDAYS.map((weekday) => (
          <div
            key={weekday}
            className="h-8 flex items-center justify-center text-xs font-medium text-gray-500"
          >
            {weekday}
          </div>
        ))}
      </div>

      {/* 日期网格 */}
      <div className="grid grid-cols-7 gap-1 mb-4">
        {calendarDays.map((dayInfo, index) => (
          <Button
            key={index}
            variant="ghost"
            size="sm"
            onClick={() => onSelect?.(dayInfo.date)}
            className={cn(
              "h-8 w-8 p-0 text-xs font-normal hover:bg-gray-100 rounded",
              !dayInfo.isCurrentMonth && "text-gray-400 opacity-60",
              isSelected(dayInfo.date) && "bg-teal-500 text-white hover:bg-teal-600",
              isToday(dayInfo.date) && !isSelected(dayInfo.date) && "bg-blue-50 text-blue-600 font-medium"
            )}
          >
            {dayInfo.day}
          </Button>
        ))}
      </div>
      
      {/* 底部按钮 */}
      <div className="flex items-center justify-end gap-2 pt-2 border-t border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={clearSelection}
          className="h-7 px-3 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100"
        >
          清空
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={goToToday}
          className="h-7 px-3 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100"
        >
          现在
        </Button>
        <Button
          size="sm"
          onClick={confirmSelection}
          className="h-7 px-3 text-xs bg-blue-500 hover:bg-blue-600 text-white"
        >
          确定
        </Button>
      </div>
    </div>
  )
}
