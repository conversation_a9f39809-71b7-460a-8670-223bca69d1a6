# IKUN ERP UI设计规范

## 📐 布局规范

### 侧边栏规范
- **桌面端侧边栏宽度**: 224px (w-56)
- **移动端侧边栏宽度**: 224px (w-56)
- **主内容区域左边距**: 224px (lg:pl-56)

### 页面布局规范 ⚠️ **重点规范**
- **页面内边距**: 16px (p-4)
- **内容容器**: 使用全宽 (w-full)，不限制最大宽度
- **⚠️ 所有页面顶部不显示页面标题大字符**: 页面内容直接从功能区域开始
- **不显示页面描述信息**: 删除所有描述性提示信息
- **右侧内容区域占比扩大**: 主要内容区域应该占据更多空间

**正确示例**:
```typescript
// ✅ 正确 - 直接显示功能组件
export default function ProductPage() {
  return (
    <DashboardLayout>
      <ProductComponent />
    </DashboardLayout>
  )
}

// ❌ 错误 - 不要添加页面标题
export default function ProductPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">产品管理</h1> {/* 删除这个 */}
        </div>
        <ProductComponent />
      </div>
    </DashboardLayout>
  )
}
```

### 底部信息栏规范 ⚠️ **重点规范**
- **⚠️ 固定到最底部**: 分页、统计信息等固定在页面底部
- **⚠️ 缩小高度**: 使用更紧凑的设计 `py-2` 而不是 `py-4`
- **背景色**: 使用 `bg-muted/30` 提供轻微的视觉分离
- **布局**: 使用 `mt-auto` 推到底部

**实现方式**:
```typescript
<div className="flex flex-col h-[calc(100vh-4rem)]">
  <Card className="flex-1 flex flex-col">
    <CardContent className="p-0 flex-1 flex flex-col">
      {/* 主要内容 */}
      <div className="flex-1 overflow-x-auto">
        {/* 表格或其他内容 */}
      </div>

      {/* 固定底部 */}
      <div className="flex items-center justify-between px-4 py-2 border-t bg-muted/30 mt-auto">
        {/* 分页信息 */}
      </div>
    </CardContent>
  </Card>
</div>
```

### 间距规范
- **组件间距**: 24px (space-y-6)
- **卡片内边距**: 16px (p-4)
- **按钮间距**: 8px (gap-2)

## 🎨 颜色规范

### 主色调
- **主色**: Blue-500 (#3B82F6)
- **主色悬停**: Blue-600 (#2563EB)
- **成功色**: Green-500 (#10B981)
- **警告色**: Orange-500 (#F59E0B)
- **错误色**: Red-500 (#EF4444)

### 状态颜色
- **待编辑**: Orange-100背景 + Orange-800文字
- **成功**: Green-100背景 + Green-800文字
- **失败**: Red-100背景 + Red-800文字
- **处理中**: Blue-100背景 + Blue-800文字

### 背景颜色
- **页面背景**: Background (白色/深色模式自适应)
- **卡片背景**: Card (白色/深色模式自适应)
- **表格头部**: Gray-50 (浅灰色)

## 🔘 按钮规范

### 按钮尺寸
- **默认按钮**: h-10 px-4 py-2
- **小按钮**: h-8 px-3 (size="sm")
- **图标按钮**: h-10 w-10 (size="icon")

### 按钮样式
- **主要按钮**: `bg-blue-500 text-white hover:bg-blue-600`
- **次要按钮**: `variant="outline"`
- **危险按钮**: `variant="destructive"`
- **成功按钮**: `bg-green-500 text-white hover:bg-green-600`

### 按钮图标
- **图标尺寸**: w-4 h-4
- **图标间距**: mr-1 (图标在文字左侧)

## 📊 表格规范

### 表格结构
- **表格容器**: 使用Card包装
- **表格头部**: 固定头部 `sticky top-0 bg-gray-50`
- **表格行**: `hover:bg-gray-50` 悬停效果
- **表格单元格**: `p-2` 内边距

### 表格列宽
- **复选框列**: w-12 (48px)
- **图片列**: w-20 (80px)
- **操作列**: w-16 (64px)
- **状态列**: w-20 (80px)
- **内容列**: 根据内容自适应，使用max-w-xs限制

### 表格内容
- **文字截断**: 使用 `truncate max-w-xs` 
- **图片尺寸**: w-10 h-10 (40x40px)
- **状态徽章**: 使用Badge组件

## 🏷️ 徽章规范

### 徽章样式
- **默认徽章**: `variant="outline"`
- **状态徽章**: 根据状态使用不同颜色类名
- **徽章尺寸**: 默认尺寸，文字使用 `text-xs`

### 状态徽章颜色
```css
.status-waiting { @apply bg-orange-100 text-orange-800; }
.status-success { @apply bg-green-100 text-green-800; }
.status-failed { @apply bg-red-100 text-red-800; }
.status-processing { @apply bg-blue-100 text-blue-800; }
```

## 📝 表单规范

### 表单布局
- **表单间距**: space-y-4
- **标签样式**: 使用Label组件
- **输入框**: 使用Input组件，默认高度 h-10
- **选择器**: 使用Select组件

### 表单验证
- **错误状态**: 使用FormMessage组件
- **必填标识**: 在标签后添加红色星号

## 🔍 搜索和筛选规范

### 搜索和筛选布局规范 ⚠️ **重点规范**
- **⚠️ 搜索重置状态栏移到右侧**: 与筛选按钮同行排列
- **左侧**: 状态筛选按钮组
- **右侧**: 搜索框 + 搜索按钮 + 重置按钮

**布局结构**:
```typescript
<div className="flex items-center justify-between gap-4">
  {/* 左侧：状态筛选 */}
  <div className="flex flex-wrap gap-2">
    {statusOptions.map((status) => (
      <Button key={status.value} size="sm" variant={...}>
        {status.label}
      </Button>
    ))}
  </div>

  {/* 右侧：搜索区域 */}
  <div className="flex items-center gap-2">
    <Input placeholder="搜索..." className="w-64" />
    <Button size="sm">搜索</Button>
    <Button size="sm" variant="outline">重置</Button>
  </div>
</div>
```

### 搜索区域
- **搜索框宽度**: w-64 (256px)
- **选择器宽度**: w-32 (128px)
- **按钮组间距**: gap-2

### 筛选按钮
- **活跃状态**: `variant="default"`
- **非活跃状态**: `variant="outline"`
- **按钮高度**: h-8
- **按钮组布局**: `flex flex-wrap gap-2`

## 📱 响应式规范

### 断点设置
- **移动端**: < 768px
- **平板端**: 768px - 1024px
- **桌面端**: > 1024px

### 移动端适配
- **侧边栏**: 覆盖式显示，带遮罩
- **表格**: 水平滚动
- **按钮**: 保持原尺寸，可换行显示
- **搜索区域**: 垂直堆叠

## 🎯 交互规范

### 悬停效果
- **按钮**: 颜色加深
- **表格行**: `hover:bg-gray-50`
- **卡片**: 可选择性添加阴影效果

### 选中状态
- **复选框**: 使用Checkbox组件
- **选中行**: 可选择性添加背景色
- **活跃按钮**: 使用主色背景

### 加载状态
- **按钮加载**: 显示加载图标
- **表格加载**: 显示骨架屏或加载提示
- **页面加载**: 显示加载指示器

## 🔧 组件使用规范

### 必须使用的组件
- **Button**: 所有按钮必须使用Button组件
- **Card**: 内容区域必须使用Card包装
- **Badge**: 状态显示必须使用Badge组件
- **Input**: 输入框必须使用Input组件
- **Select**: 下拉选择必须使用Select组件
- **Checkbox**: 复选框必须使用Checkbox组件

### 禁止使用的元素
- **原生button**: 禁止使用原生button标签
- **原生input**: 禁止使用原生input标签
- **原生select**: 禁止使用原生select标签
- **内联样式**: 禁止使用style属性

## 📏 尺寸规范

### 图标尺寸
- **按钮图标**: w-4 h-4 (16px)
- **导航图标**: w-5 h-5 (20px)
- **大图标**: w-6 h-6 (24px)

### 头像尺寸
- **小头像**: w-8 h-8 (32px)
- **默认头像**: w-10 h-10 (40px)
- **大头像**: w-12 h-12 (48px)

### 输入框尺寸
- **默认高度**: h-10 (40px)
- **小尺寸**: h-8 (32px)
- **大尺寸**: h-12 (48px)

## 🎨 主题规范

### 深色模式支持
- **所有组件必须支持深色模式**
- **使用CSS变量定义颜色**
- **避免硬编码颜色值**

### 颜色变量
```css
--background: 主背景色
--foreground: 主文字色
--card: 卡片背景色
--card-foreground: 卡片文字色
--primary: 主色
--primary-foreground: 主色文字色
```

## ✅ 检查清单

### ⚠️ 重点规范检查 (必须严格遵守)
- [ ] **页面顶部没有大标题字符** (如"产品管理"、"仪表板"等)
- [ ] **底部信息栏固定在最底部且高度紧凑** (使用py-2而不是py-4)
- [ ] **搜索和重置按钮在右侧，与筛选按钮同行**
- [ ] **删除所有描述性提示信息** (如"管理您的产品库存和信息")

### 开发前检查
- [ ] 确认使用正确的布局尺寸
- [ ] 确认不添加页面描述信息
- [ ] 确认使用统一的组件库
- [ ] 确认页面布局符合重点规范

### 开发中检查
- [ ] 所有按钮使用Button组件
- [ ] 所有表单使用Form相关组件
- [ ] 所有状态使用Badge组件
- [ ] 响应式设计正确实现
- [ ] 搜索筛选布局正确

### 开发后检查
- [ ] 深色模式正常显示
- [ ] 移动端适配正确
- [ ] 交互效果符合规范
- [ ] 无硬编码样式
- [ ] 重点规范全部符合

---

## 🚨 重要提醒

**此规范为强制性规范，所有开发人员必须严格遵守。**

### ⚠️ 特别注意的重点规范：
1. **所有页面顶部不显示页面标题大字符** - 这是用户明确要求的重点规范
2. **底部信息栏固定到最底部并缩小高度** - 提高空间利用率和美观度
3. **搜索重置状态栏移到右侧与筛选按钮同行** - 优化布局和用户体验

### 违规后果：
- 任何偏离此规范的代码都需要重新修改
- 特别是重点规范，必须100%遵守
- 代码审查时会重点检查这些规范的执行情况

### 记忆要点：
- **无标题**: 页面直接显示功能，不要大标题
- **底部固定**: 分页等信息固定底部，高度紧凑
- **右侧搜索**: 搜索功能放在右侧，与筛选同行
- **标签筛选**: 状态筛选使用标签按钮，单选模式，紧凑布局
- **翻译按钮**: 翻译功能使用小尺寸按钮，带图标，支持加载状态
