"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态\n    const [selectedStatuses, setSelectedStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTranslationStatuses, setSelectedTranslationStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数\n    const handleStatusToggle = (status)=>{\n        setSelectedStatuses((prev)=>prev.includes(status) ? prev.filter((s)=>s !== status) : [\n                ...prev,\n                status\n            ]);\n    };\n    const handleTranslationStatusToggle = (status)=>{\n        setSelectedTranslationStatuses((prev)=>prev.includes(status) ? prev.filter((s)=>s !== status) : [\n                ...prev,\n                status\n            ]);\n    };\n    const handleReset = ()=>{\n        setSelectedStatuses([]);\n        setSelectedTranslationStatuses([]);\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatuses.length > 0) {\n            params.status = selectedStatuses.join(\",\");\n        }\n        if (selectedTranslationStatuses.length > 0) {\n            params.translation_status = selectedTranslationStatuses.join(\",\");\n        }\n        if (dateRange.start) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end) {\n            params.end_date = dateRange.end;\n        }\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatuses,\n        selectedTranslationStatuses,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        // 准备进度数据\n        const selectedProductsData = uploadProducts.filter((p)=>selectedProducts.includes(p.id));\n        const progressItems = selectedProductsData.map((product)=>({\n                id: product.id,\n                sku: product.sku,\n                name: product.english_title || \"产品 \".concat(product.id)\n            }));\n        // 启动进度对话框\n        batchTranslationProgress.startTranslation(progressItems);\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 模拟逐个产品翻译进度（实际上后端是批量处理的）\n            selectedProducts.forEach((productId, index)=>{\n                setTimeout(()=>{\n                    batchTranslationProgress.setProcessingItem(productId);\n                }, index * 100) // 每100ms标记一个产品为处理中\n                ;\n            });\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                const details = (results === null || results === void 0 ? void 0 : results.details) || [];\n                // 更新每个产品的进度状态\n                details.forEach((detail)=>{\n                    if (detail.success) {\n                        batchTranslationProgress.setSuccessItem(detail.productId, \"翻译完成\");\n                    } else {\n                        batchTranslationProgress.setErrorItem(detail.productId, detail.error || \"翻译失败\");\n                    }\n                });\n                // 标记翻译完成\n                batchTranslationProgress.finishTranslation();\n                setSelectedProducts([]);\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            // 标记所有产品为失败\n            selectedProducts.forEach((productId)=>{\n                batchTranslationProgress.setErrorItem(productId, error instanceof Error ? error.message : \"翻译失败\");\n            });\n            // 标记翻译完成\n            batchTranslationProgress.finishTranslation();\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 604,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 656,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 655,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 668,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 667,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 717,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"全部\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 768,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"状态\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStatuses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: selectedStatuses.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: statusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuCheckboxItem, {\n                                                                        checked: selectedStatuses.includes(option.value),\n                                                                        onCheckedChange: ()=>handleStatusToggle(option.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                lineNumber: 785,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, option.value, true, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 780,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 796,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"翻译状态\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 797,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedTranslationStatuses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: selectedTranslationStatuses.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 803,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: translationStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuCheckboxItem, {\n                                                                        checked: selectedTranslationStatuses.includes(option.value),\n                                                                        onCheckedChange: ()=>handleTranslationStatusToggle(option.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, option.value, true, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 808,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 824,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 825,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-64 p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 837,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.start,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                start: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 838,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 836,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 846,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.end,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                end: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 847,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 872,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 895,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 898,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 928,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 977,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 991,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 989,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1002,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1015,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1023,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 1022,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1028,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1027,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1032,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1031,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1035,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1038,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1037,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1044,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1043,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1051,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1050,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1055,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1054,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1061,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1060,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1064,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1069,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1065,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 885,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1084,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1088,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1087,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1083,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 683,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 682,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1131,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1143,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.BatchTranslationProgress, {\n                open: batchTranslationProgress.isOpen,\n                onOpenChange: batchTranslationProgress.setIsOpen,\n                items: batchTranslationProgress.items,\n                isProcessing: batchTranslationProgress.isProcessing,\n                onCancel: batchTranslationProgress.cancelTranslation,\n                onComplete: async ()=>{\n                    // 翻译完成后刷新数据\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    // 延迟关闭对话框，让用户看到完成状态\n                    setTimeout(()=>{\n                        batchTranslationProgress.closeDialog();\n                    }, 2000);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 681,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"AyWzKMoVGB62XyLBNEOJtbP8lfQ=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});