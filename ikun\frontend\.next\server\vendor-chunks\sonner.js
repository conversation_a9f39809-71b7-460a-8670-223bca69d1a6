"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ function __insertCSS(code) {\n    if (!code || typeof document == \"undefined\") return;\n    let head = document.head || document.getElementsByTagName(\"head\")[0];\n    let style = document.createElement(\"style\");\n    style.type = \"text/css\";\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case \"success\":\n            return SuccessIcon;\n        case \"info\":\n            return InfoIcon;\n        case \"warning\":\n            return WarningIcon;\n        case \"error\":\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            \"sonner-loading-wrapper\",\n            className\n        ].filter(Boolean).join(\" \"),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const callback = ()=>{\n            setIsDocumentHidden(document.hidden);\n        };\n        document.addEventListener(\"visibilitychange\", callback);\n        return ()=>window.removeEventListener(\"visibilitychange\", callback);\n    }, []);\n    return isDocumentHidden;\n};\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === \"number\" || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: \"error\"\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"success\",\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"info\",\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"warning\",\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"loading\",\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: \"loading\",\n                    message: data.loading,\n                    description: typeof data.description !== \"function\" ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    \"resolve\",\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: \"default\",\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === \"function\" ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === \"function\" ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"error\",\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === \"function\" ? await data.error(response) : data.error;\n                    const description = typeof data.description === \"function\" ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"error\",\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === \"function\" ? await data.success(response) : data.success;\n                    const description = typeof data.description === \"function\" ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"success\",\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    \"reject\",\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === \"function\" ? await data.error(error) : data.error;\n                    const description = typeof data.description === \"function\" ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"error\",\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === \"reject\" ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== \"string\" && typeof id !== \"number\") {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === \"object\" && \"ok\" in data && typeof data.ok === \"boolean\" && \"status\" in data && typeof data.status === \"number\";\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = \"24px\";\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = \"16px\";\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split(\"-\");\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = \"\", descriptionClassName = \"\", duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = \"Close toast\" } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || \"\";\n    const toastDescriptionClassname = toast.descriptionClassName || \"\";\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>heights.findIndex((height)=>height.toastId === toast.id) || 0, [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var _toast_closeButton;\n        return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n    }, [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>toast.duration || durationFromToaster || TOAST_LIFETIME, [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split(\"-\");\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return heights.reduce((prev, curr, reducerIndex)=>{\n            // Calculate offset up until current toast\n            if (reducerIndex >= heightIndex) {\n                return prev;\n            }\n            return prev + curr.height;\n        }, 0);\n    }, [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === \"loading\";\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>heightIndex * gap + toastsHeightBefore, [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        remainingTime.current = duration;\n    }, [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        setMounted(true);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const toastNode = toastRef.current;\n        if (toastNode) {\n            const height = toastNode.getBoundingClientRect().height;\n            // Add toast height to heights array after the toast is mounted\n            setInitialHeight(height);\n            setHeights((h)=>[\n                    {\n                        toastId: toast.id,\n                        height,\n                        position: toast.position\n                    },\n                    ...h\n                ]);\n            return ()=>setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        }\n    }, [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        // Keep height up to date with the content in case it updates\n        if (!mounted) return;\n        const toastNode = toastRef.current;\n        const originalHeight = toastNode.style.height;\n        toastNode.style.height = \"auto\";\n        const newHeight = toastNode.getBoundingClientRect().height;\n        toastNode.style.height = originalHeight;\n        setInitialHeight(newHeight);\n        setHeights((heights)=>{\n            const alreadyExists = heights.find((height)=>height.toastId === toast.id);\n            if (!alreadyExists) {\n                return [\n                    {\n                        toastId: toast.id,\n                        height: newHeight,\n                        position: toast.position\n                    },\n                    ...heights\n                ];\n            } else {\n                return heights.map((height)=>height.toastId === toast.id ? {\n                        ...height,\n                        height: newHeight\n                    } : height);\n            }\n        });\n    }, [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // Save the offset for the exit swipe animation\n        setRemoved(true);\n        setOffsetBeforeRemove(offset.current);\n        setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        setTimeout(()=>{\n            removeToast(toast);\n        }, TIME_BEFORE_UNMOUNT);\n    }, [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (toast.promise && toastType === \"loading\" || toast.duration === Infinity || toast.type === \"loading\") return;\n        let timeoutId;\n        // Pause the timer on each hover\n        const pauseTimer = ()=>{\n            if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                // Get the elapsed time since the timer started\n                const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                remainingTime.current = remainingTime.current - elapsedTime;\n            }\n            lastCloseTimerStartTimeRef.current = new Date().getTime();\n        };\n        const startTimer = ()=>{\n            // setTimeout(, Infinity) behaves as if the delay is 0.\n            // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n            // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n            if (remainingTime.current === Infinity) return;\n            closeTimerStartTimeRef.current = new Date().getTime();\n            // Let the toast know it has started\n            timeoutId = setTimeout(()=>{\n                toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                deleteToast();\n            }, remainingTime.current);\n        };\n        if (expanded || interacting || isDocumentHidden) {\n            pauseTimer();\n        } else {\n            startTimer();\n        }\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (toast.delete) {\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        }\n    }, [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, \"sonner-loader\"),\n                \"data-visible\": toastType === \"loading\"\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === \"loading\"\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            \"--index\": index,\n            \"--toasts-before\": index,\n            \"--z-index\": toasts.length - index,\n            \"--offset\": `${removed ? offsetBeforeRemove : offset.current}px`,\n            \"--initial-height\": expandByDefault ? \"auto\" : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === \"BUTTON\") return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue(\"--swipe-amount-x\").replace(\"px\", \"\")) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue(\"--swipe-amount-y\").replace(\"px\", \"\")) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === \"x\" ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === \"x\") {\n                    setSwipeOutDirection(swipeAmountX > 0 ? \"right\" : \"left\");\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? \"down\" : \"up\");\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty(\"--swipe-amount-x\", `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty(\"--swipe-amount-y\", `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? \"x\" : \"y\");\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === \"y\") {\n                // Handle vertical swipes\n                if (swipeDirections.includes(\"top\") || swipeDirections.includes(\"bottom\")) {\n                    if (swipeDirections.includes(\"top\") && yDelta < 0 || swipeDirections.includes(\"bottom\") && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === \"x\") {\n                // Handle horizontal swipes\n                if (swipeDirections.includes(\"left\") || swipeDirections.includes(\"right\")) {\n                    if (swipeDirections.includes(\"left\") && xDelta < 0 || swipeDirections.includes(\"right\") && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty(\"--swipe-amount-x\", `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty(\"--swipe-amount-y\", `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== \"loading\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === \"loading\" && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== \"loading\" ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === \"function\" ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === \"function\" ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (true) return \"ltr\";\n    if (typeof document === \"undefined\") return \"ltr\"; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute(\"dir\");\n    if (dirAttribute === \"auto\" || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? \"--mobile-offset\" : \"--offset\";\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === \"number\" ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === \"number\" || typeof offset === \"string\") {\n            assignAll(offset);\n        } else if (typeof offset === \"object\") {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === \"number\" ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                setTimeout(()=>{\n                    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                        setActiveToasts((toasts)=>toasts.filter((t)=>t.id !== toast.id));\n                    });\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    setActiveToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Toaster(props, ref) {\n    const { invert, position = \"bottom-right\", hotkey = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand, closeButton, className, offset, mobileOffset, theme = \"light\", richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = \"Notifications\" } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return Array.from(new Set([\n            position\n        ].concat(toasts.filter((toast)=>toast.position).map((toast)=>toast.position))));\n    }, [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== \"system\" ? theme :  false ? 0 : \"light\");\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((toastToRemove)=>{\n        setToasts((toasts)=>{\n            var _toasts_find;\n            if (!((_toasts_find = toasts.find((toast)=>toast.id === toastToRemove.id)) == null ? void 0 : _toasts_find.delete)) {\n                ToastState.dismiss(toastToRemove.id);\n            }\n            return toasts.filter(({ id })=>id !== toastToRemove.id);\n        });\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                // Prevent batching of other state updates\n                requestAnimationFrame(()=>{\n                    setToasts((toasts)=>toasts.map((t)=>t.id === toast.id ? {\n                                ...t,\n                                delete: true\n                            } : t));\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    setToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (theme !== \"system\") {\n            setActualTheme(theme);\n            return;\n        }\n        if (theme === \"system\") {\n            // check if current preference is dark\n            if (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches) {\n                // it's currently dark\n                setActualTheme(\"dark\");\n            } else {\n                // it's not dark\n                setActualTheme(\"light\");\n            }\n        }\n        if (true) return;\n        const darkMediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        try {\n            // Chrome & Firefox\n            darkMediaQuery.addEventListener(\"change\", ({ matches })=>{\n                if (matches) {\n                    setActualTheme(\"dark\");\n                } else {\n                    setActualTheme(\"light\");\n                }\n            });\n        } catch (error) {\n            // Safari < 14\n            darkMediaQuery.addListener(({ matches })=>{\n                try {\n                    if (matches) {\n                        setActualTheme(\"dark\");\n                    } else {\n                        setActualTheme(\"light\");\n                    }\n                } catch (e) {\n                    console.error(e);\n                }\n            });\n        }\n    }, [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Ensure expanded is always false when no toasts are present / only one left\n        if (toasts.length <= 1) {\n            setExpanded(false);\n        }\n    }, [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            var _listRef_current;\n            const isHotkeyPressed = hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) {\n                var _listRef_current1;\n                setExpanded(true);\n                (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n            }\n            if (event.code === \"Escape\" && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                setExpanded(false);\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (listRef.current) {\n            return ()=>{\n                if (lastFocusedElementRef.current) {\n                    lastFocusedElementRef.current.focus({\n                        preventScroll: true\n                    });\n                    lastFocusedElementRef.current = null;\n                    isFocusWithinRef.current = false;\n                }\n            };\n        }\n    }, [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split(\"-\");\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === \"auto\" ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                \"--front-toast-height\": `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                \"--width\": `${TOAST_WIDTH}px`,\n                \"--gap\": `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === \"false\";\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === \"false\";\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;