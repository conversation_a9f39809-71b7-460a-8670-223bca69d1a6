"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { CheckCircle, XCircle, AlertCircle, Loader2, X } from "lucide-react"
import { cn } from "@/lib/utils"

export interface ProgressItem {
  id: string | number
  name: string
  status: 'pending' | 'processing' | 'success' | 'error'
  message?: string
  error?: string
}

export interface ProgressDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  items: ProgressItem[]
  progress: number
  isProcessing: boolean
  canCancel?: boolean
  onCancel?: () => void
  showDetails?: boolean
  successMessage?: string
  errorMessage?: string
}

export function ProgressDialog({
  open,
  onOpenChange,
  title,
  items,
  progress,
  isProcessing,
  canCancel = true,
  onCancel,
  showDetails = true,
  successMessage,
  errorMessage
}: ProgressDialogP<PERSON>) {
  const completedItems = items.filter(item => item.status === 'success' || item.status === 'error')
  const successCount = items.filter(item => item.status === 'success').length
  const errorCount = items.filter(item => item.status === 'error').length
  const totalCount = items.length

  const getStatusIcon = (status: ProgressItem['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />
    }
  }

  const getOverallStatus = () => {
    if (isProcessing) {
      return {
        icon: <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />,
        text: '处理中...',
        color: 'text-blue-600'
      }
    }
    
    if (errorCount > 0 && successCount > 0) {
      return {
        icon: <AlertCircle className="w-5 h-5 text-yellow-600" />,
        text: '部分完成',
        color: 'text-yellow-600'
      }
    }
    
    if (errorCount > 0) {
      return {
        icon: <XCircle className="w-5 h-5 text-red-600" />,
        text: '处理失败',
        color: 'text-red-600'
      }
    }
    
    if (successCount === totalCount && totalCount > 0) {
      return {
        icon: <CheckCircle className="w-5 h-5 text-green-600" />,
        text: '全部完成',
        color: 'text-green-600'
      }
    }
    
    return {
      icon: <AlertCircle className="w-5 h-5 text-gray-600" />,
      text: '等待处理',
      color: 'text-gray-600'
    }
  }

  const overallStatus = getOverallStatus()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              {overallStatus.icon}
              {title}
            </DialogTitle>
            {!isProcessing && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* 进度条 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className={overallStatus.color}>{overallStatus.text}</span>
              <span className="text-muted-foreground">
                {completedItems.length}/{totalCount}
              </span>
            </div>
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>进度: {Math.round(progress)}%</span>
              {successCount > 0 && errorCount > 0 && (
                <span>成功 {successCount} · 失败 {errorCount}</span>
              )}
            </div>
          </div>

          {/* 状态消息 */}
          {!isProcessing && (
            <div className="text-sm">
              {successCount === totalCount && successMessage && (
                <div className="text-green-600 bg-green-50 p-2 rounded">
                  {successMessage}
                </div>
              )}
              {errorCount > 0 && errorMessage && (
                <div className="text-red-600 bg-red-50 p-2 rounded">
                  {errorMessage}
                </div>
              )}
            </div>
          )}

          {/* 详细列表 */}
          {showDetails && items.length > 0 && (
            <div className="max-h-48 overflow-y-auto space-y-1">
              <div className="text-xs font-medium text-muted-foreground mb-2">
                处理详情
              </div>
              {items.map((item) => (
                <div
                  key={item.id}
                  className={cn(
                    "flex items-center gap-2 p-2 rounded text-xs",
                    item.status === 'success' && "bg-green-50",
                    item.status === 'error' && "bg-red-50",
                    item.status === 'processing' && "bg-blue-50"
                  )}
                >
                  {getStatusIcon(item.status)}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{item.name}</div>
                    {item.message && (
                      <div className="text-muted-foreground truncate">
                        {item.message}
                      </div>
                    )}
                    {item.error && (
                      <div className="text-red-600 truncate">
                        {item.error}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end gap-2">
            {isProcessing && canCancel && onCancel && (
              <Button variant="outline" size="sm" onClick={onCancel}>
                取消
              </Button>
            )}
            {!isProcessing && (
              <Button size="sm" onClick={() => onOpenChange(false)}>
                关闭
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
