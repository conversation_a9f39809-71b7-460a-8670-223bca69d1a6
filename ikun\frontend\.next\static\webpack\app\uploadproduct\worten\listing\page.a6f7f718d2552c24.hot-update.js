"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/uploadproduct/worten/listing/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x-circle.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ XCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst XCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"XCircle\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"m15 9-6 6\", key: \"1uzhvr\" }],\n  [\"path\", { d: \"m9 9 6 6\", key: \"z0biqf\" }]\n]);\n\n\n//# sourceMappingURL=x-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC1jaXJjbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxnQkFBZ0IsZ0VBQWdCO0FBQ2hDLGVBQWUsNENBQTRDO0FBQzNELGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsOEJBQThCO0FBQzNDOztBQUU4QjtBQUM5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3gtY2lyY2xlLmpzPzY0ZDIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBsdWNpZGUtcmVhY3QgdjAuMjkyLjAgLSBJU0NcbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgWENpcmNsZSA9IGNyZWF0ZUx1Y2lkZUljb24oXCJYQ2lyY2xlXCIsIFtcbiAgW1wiY2lyY2xlXCIsIHsgY3g6IFwiMTJcIiwgY3k6IFwiMTJcIiwgcjogXCIxMFwiLCBrZXk6IFwiMW1nbGF5XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIm0xNSA5LTYgNlwiLCBrZXk6IFwiMXV6aHZyXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIm05IDkgNiA2XCIsIGtleTogXCJ6MGJpcWZcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IFhDaXJjbGUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9eC1jaXJjbGUuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx":
/*!**********************************************************!*\
  !*** ./src/components/ui/batch-translation-progress.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BatchTranslationProgress: function() { return /* binding */ BatchTranslationProgress; },\n/* harmony export */   useBatchTranslationProgress: function() { return /* binding */ useBatchTranslationProgress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _progress_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./progress-dialog */ \"(app-pages-browser)/./src/components/ui/progress-dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ BatchTranslationProgress,useBatchTranslationProgress auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction BatchTranslationProgress(param) {\n    let { open, onOpenChange, items, isProcessing, onCancel, onComplete } = param;\n    _s();\n    const [progress, setProgress] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    // 转换为通用进度项格式\n    const progressItems = items.map((item)=>({\n            id: item.productId,\n            name: \"\".concat(item.sku, \" - \").concat(item.name),\n            status: item.status,\n            message: item.message,\n            error: item.error\n        }));\n    // 计算进度\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const completedCount = items.filter((item)=>item.status === \"success\" || item.status === \"error\").length;\n        const newProgress = items.length > 0 ? completedCount / items.length * 100 : 0;\n        setProgress(newProgress);\n        // 如果全部完成且不在处理中，触发完成回调\n        if (completedCount === items.length && items.length > 0 && !isProcessing) {\n            setTimeout(()=>{\n                onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n            }, 1000) // 延迟1秒让用户看到完成状态\n            ;\n        }\n    }, [\n        items,\n        isProcessing,\n        onComplete\n    ]);\n    const successCount = items.filter((item)=>item.status === \"success\").length;\n    const errorCount = items.filter((item)=>item.status === \"error\").length;\n    const getSuccessMessage = ()=>{\n        if (successCount === items.length) {\n            return \"所有 \".concat(items.length, \" 个产品翻译完成！\");\n        }\n        if (successCount > 0) {\n            return \"成功翻译 \".concat(successCount, \" 个产品\");\n        }\n        return undefined;\n    };\n    const getErrorMessage = ()=>{\n        if (errorCount === items.length) {\n            return \"所有 \".concat(items.length, \" 个产品翻译失败，请检查后重试\");\n        }\n        if (errorCount > 0) {\n            return \"\".concat(errorCount, \" 个产品翻译失败\");\n        }\n        return undefined;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_progress_dialog__WEBPACK_IMPORTED_MODULE_2__.ProgressDialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        title: \"批量翻译进度\",\n        items: progressItems,\n        progress: progress,\n        isProcessing: isProcessing,\n        canCancel: true,\n        onCancel: onCancel,\n        showDetails: true,\n        successMessage: getSuccessMessage(),\n        errorMessage: getErrorMessage()\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\batch-translation-progress.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(BatchTranslationProgress, \"ZVQpwjU6Dz5R8VBOzPsnxGRmMVo=\");\n_c = BatchTranslationProgress;\n// Hook for managing batch translation progress\nfunction useBatchTranslationProgress() {\n    _s1();\n    const [items, setItems] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const [isProcessing, setIsProcessing] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const startTranslation = (productList)=>{\n        const initialItems = productList.map((product)=>({\n                productId: product.id,\n                sku: product.sku,\n                name: product.name,\n                status: \"pending\",\n                message: \"等待翻译...\"\n            }));\n        setItems(initialItems);\n        setIsProcessing(true);\n        setIsOpen(true);\n    };\n    const updateItemStatus = (productId, status, message, error)=>{\n        setItems((prev)=>prev.map((item)=>item.productId === productId ? {\n                    ...item,\n                    status,\n                    message,\n                    error\n                } : item));\n    };\n    const setProcessingItem = (productId)=>{\n        updateItemStatus(productId, \"processing\", \"正在翻译...\");\n    };\n    const setSuccessItem = (productId, message)=>{\n        updateItemStatus(productId, \"success\", message || \"翻译完成\");\n    };\n    const setErrorItem = (productId, error)=>{\n        updateItemStatus(productId, \"error\", undefined, error);\n    };\n    const finishTranslation = ()=>{\n        setIsProcessing(false);\n    };\n    const cancelTranslation = ()=>{\n        setIsProcessing(false);\n        setIsOpen(false);\n        setItems([]);\n    };\n    const closeDialog = ()=>{\n        setIsOpen(false);\n        setItems([]);\n    };\n    return {\n        items,\n        isProcessing,\n        isOpen,\n        startTranslation,\n        setProcessingItem,\n        setSuccessItem,\n        setErrorItem,\n        finishTranslation,\n        cancelTranslation,\n        closeDialog,\n        setIsOpen\n    };\n}\n_s1(useBatchTranslationProgress, \"q71jHa2BGVvl/cXyQZQ9LY8YO6M=\");\nvar _c;\n$RefreshReg$(_c, \"BatchTranslationProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/progress-dialog.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/progress-dialog.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressDialog: function() { return /* binding */ ProgressDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ProgressDialog auto */ \n\n\n\n\n\n\nfunction ProgressDialog(param) {\n    let { open, onOpenChange, title, items, progress, isProcessing, canCancel = true, onCancel, showDetails = true, successMessage, errorMessage } = param;\n    const completedItems = items.filter((item)=>item.status === \"success\" || item.status === \"error\");\n    const successCount = items.filter((item)=>item.status === \"success\").length;\n    const errorCount = items.filter((item)=>item.status === \"error\").length;\n    const totalCount = items.length;\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-600 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getOverallStatus = ()=>{\n        if (isProcessing) {\n            return {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-600 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 15\n                }, this),\n                text: \"处理中...\",\n                color: \"text-blue-600\"\n            };\n        }\n        if (errorCount > 0 && successCount > 0) {\n            return {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 15\n                }, this),\n                text: \"部分完成\",\n                color: \"text-yellow-600\"\n            };\n        }\n        if (errorCount > 0) {\n            return {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 15\n                }, this),\n                text: \"处理失败\",\n                color: \"text-red-600\"\n            };\n        }\n        if (successCount === totalCount && totalCount > 0) {\n            return {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 15\n                }, this),\n                text: \"全部完成\",\n                color: \"text-green-600\"\n            };\n        }\n        return {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                lineNumber: 97,\n                columnNumber: 13\n            }, this),\n            text: \"等待处理\",\n            color: \"text-gray-600\"\n        };\n    };\n    const overallStatus = getOverallStatus();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    overallStatus.icon,\n                                    title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this),\n                            !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>onOpenChange(false),\n                                className: \"h-6 w-6 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: overallStatus.color,\n                                            children: overallStatus.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                completedItems.length,\n                                                \"/\",\n                                                totalCount\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_3__.Progress, {\n                                    value: progress,\n                                    className: \"h-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"进度: \",\n                                                Math.round(progress),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        successCount > 0 && errorCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"成功 \",\n                                                successCount,\n                                                \" \\xb7 失败 \",\n                                                errorCount\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                successCount === totalCount && successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-green-600 bg-green-50 p-2 rounded\",\n                                    children: successMessage\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 17\n                                }, this),\n                                errorCount > 0 && errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-600 bg-red-50 p-2 rounded\",\n                                    children: errorMessage\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        showDetails && items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-48 overflow-y-auto space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-muted-foreground mb-2\",\n                                    children: \"处理详情\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-2 rounded text-xs\", item.status === \"success\" && \"bg-green-50\", item.status === \"error\" && \"bg-red-50\", item.status === \"processing\" && \"bg-blue-50\"),\n                                        children: [\n                                            getStatusIcon(item.status),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium truncate\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-muted-foreground truncate\",\n                                                        children: item.message\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-600 truncate\",\n                                                        children: item.error\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-2\",\n                            children: [\n                                isProcessing && canCancel && onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: onCancel,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this),\n                                !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"关闭\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\ui\\\\progress-dialog.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_c = ProgressDialog;\nvar _c;\n$RefreshReg$(_c, \"ProgressDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/progress-dialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/uploadproduct/worten/worten-listing-page.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProductPageFull: function() { return /* binding */ UploadProductPageFull; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useUploadProducts */ \"(app-pages-browser)/./src/hooks/useUploadProducts.ts\");\n/* harmony import */ var _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useStores */ \"(app-pages-browser)/./src/hooks/useStores.ts\");\n/* harmony import */ var _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDropshipProducts */ \"(app-pages-browser)/./src/hooks/useDropshipProducts.ts\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/uploadproduct/upload-product-form */ \"(app-pages-browser)/./src/components/uploadproduct/upload-product-form.tsx\");\n/* harmony import */ var _components_translation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/translation */ \"(app-pages-browser)/./src/components/translation/index.ts\");\n/* harmony import */ var _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/batch-translation-progress */ \"(app-pages-browser)/./src/components/ui/batch-translation-progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChevronDown,Clock,Copy,Download,Edit,ExternalLink,FileText,Filter,Languages,MoreHorizontal,Plus,RefreshCw,RotateCcw,Search,Store,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ UploadProductPageFull auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 状态选项\nconst statusOptions = [\n    {\n        value: \"draft\",\n        label: \"草稿\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"pending\",\n        label: \"待上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"active\",\n        label: \"已上架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        value: \"failed\",\n        label: \"上架失败\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    },\n    {\n        value: \"inactive\",\n        label: \"已下架\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n// 翻译状态选项\nconst translationStatusOptions = [\n    {\n        value: \"pending\",\n        label: \"待翻译\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        value: \"completed\",\n        label: \"已完成\",\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction UploadProductPageFull(param) {\n    let { platform } = param;\n    var _translationProduct_multi_descriptions;\n    _s();\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // 筛选状态\n    const [selectedStatuses, setSelectedStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTranslationStatuses, setSelectedTranslationStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start: \"\",\n        end: \"\"\n    });\n    // 翻译模态框状态\n    const [translationModalOpen, setTranslationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationProduct, setTranslationProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [translationContentType, setTranslationContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"title\");\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 强制刷新key\n    ;\n    // 批量翻译进度管理\n    const batchTranslationProgress = (0,_components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress)();\n    // 使用API hooks\n    const { uploadProducts, loading: productsLoading, error: productsError, pagination, fetchUploadProducts, createUploadProduct, updateUploadProduct, deleteUploadProduct } = (0,_hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts)(platform);\n    const { stores, loading: storesLoading, fetchStores } = (0,_hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores)();\n    const { dropshipProducts, loading: dropshipLoading, fetchDropshipProducts } = (0,_hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts)();\n    // 获取当前平台的店铺\n    const platformStores = stores.filter((store)=>store.platform_code === platform);\n    // 筛选处理函数\n    const handleStatusToggle = (status)=>{\n        setSelectedStatuses((prev)=>prev.includes(status) ? prev.filter((s)=>s !== status) : [\n                ...prev,\n                status\n            ]);\n    };\n    const handleTranslationStatusToggle = (status)=>{\n        setSelectedTranslationStatuses((prev)=>prev.includes(status) ? prev.filter((s)=>s !== status) : [\n                ...prev,\n                status\n            ]);\n    };\n    const handleReset = ()=>{\n        setSelectedStatuses([]);\n        setSelectedTranslationStatuses([]);\n        setDateRange({\n            start: \"\",\n            end: \"\"\n        });\n        setSearchValue(\"\");\n        setSelectedStore(\"all\");\n        // 重新获取所有产品\n        fetchUploadProducts();\n    };\n    // 应用筛选\n    const handleApplyFilters = ()=>{\n        const params = {};\n        if (searchValue) {\n            params.search = searchValue;\n        }\n        if (selectedStore && selectedStore !== \"all\") {\n            params.store_id = selectedStore;\n        }\n        if (selectedStatuses.length > 0) {\n            params.status = selectedStatuses.join(\",\");\n        }\n        if (selectedTranslationStatuses.length > 0) {\n            params.translation_status = selectedTranslationStatuses.join(\",\");\n        }\n        if (dateRange.start) {\n            params.start_date = dateRange.start;\n        }\n        if (dateRange.end) {\n            params.end_date = dateRange.end;\n        }\n        fetchUploadProducts(params);\n    };\n    // 监听筛选条件变化，自动应用筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            handleApplyFilters();\n        }, 500) // 防抖处理\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        searchValue,\n        selectedStore,\n        selectedStatuses,\n        selectedTranslationStatuses,\n        dateRange\n    ]);\n    // 表单处理函数\n    const handleCreateProduct = async (data)=>{\n        await createUploadProduct(data);\n        setShowForm(false);\n    };\n    // 编辑产品处理函数\n    const handleUpdateProduct = async (data)=>{\n        if (!editingProduct) {\n            toast({\n                title: \"错误\",\n                description: \"编辑产品信息不存在\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            await updateUploadProduct(editingProduct.id, data);\n            toast({\n                title: \"成功\",\n                description: \"产品更新成功\"\n            });\n            // 刷新产品列表\n            await fetchUploadProducts();\n            setShowForm(false);\n            setEditingProduct(null);\n        } catch (error) {\n            console.error(\"更新产品失败:\", error);\n            toast({\n                title: \"错误\",\n                description: \"更新产品失败，请重试\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    // 根据模式选择处理函数\n    const handleFormSubmit = editingProduct ? handleUpdateProduct : handleCreateProduct;\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowForm(true);\n    };\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n        setEditingProduct(null);\n    };\n    // 初始化数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStores();\n        fetchUploadProducts();\n        fetchDropshipProducts();\n    }, [\n        platform\n    ]);\n    const handleDeleteProduct = async (productId)=>{\n        const confirmed = await confirm({\n            title: \"删除产品\",\n            description: \"确定要删除这个上架产品吗？此操作不可撤销。\",\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            await deleteUploadProduct(productId);\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n            toast({\n                description: \"产品已成功删除\",\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"删除产品失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchDelete = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要删除的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量删除\",\n            description: \"确定要删除选中的 \".concat(selectedProducts.length, \" 个产品吗？此操作不可撤销。\"),\n            confirmText: \"删除 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            // 批量删除产品\n            await Promise.all(selectedProducts.map((id)=>deleteUploadProduct(id)));\n            setSelectedProducts([]);\n            toast({\n                description: \"已成功删除 \".concat(selectedProducts.length, \" 个产品\"),\n                variant: \"default\"\n            });\n        } catch (error) {\n            console.error(\"批量删除失败:\", error);\n            toast({\n                description: \"删除产品时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBatchTranslation = async ()=>{\n        if (selectedProducts.length === 0) {\n            await confirm({\n                title: \"提示\",\n                description: \"请先选择要翻译的产品\",\n                confirmText: \"知道了\",\n                variant: \"info\",\n                icon: true\n            });\n            return;\n        }\n        const confirmed = await confirm({\n            title: \"批量翻译\",\n            description: \"确定要翻译选中的 \".concat(selectedProducts.length, \" 个产品吗？将翻译产品的标题、描述和卖点。\"),\n            confirmText: \"翻译 \".concat(selectedProducts.length, \" 个产品\"),\n            cancelText: \"取消\",\n            variant: \"default\"\n        });\n        if (!confirmed) {\n            return;\n        }\n        try {\n            var _result_data;\n            // 获取认证 token\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                throw new Error(\"请先登录\");\n            }\n            // 调用批量翻译 API\n            const response = await fetch(\"/api/v1/translation/batch/forbatch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    platform: \"worten\",\n                    source: \"form_batch\",\n                    sourceLang: \"en\",\n                    targetLangs: getPlatformTargetLanguages(\"worten\"),\n                    productids: selectedProducts.join(\",\")\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"批量翻译请求失败\");\n            }\n            const result = await response.json();\n            // 检查响应状态 - 后端返回 code: 200 表示成功\n            if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.success)) {\n                setSelectedProducts([]);\n                // 解析翻译结果\n                const { results } = result.data;\n                const successCount = (results === null || results === void 0 ? void 0 : results.successful) || 0;\n                const failedCount = (results === null || results === void 0 ? void 0 : results.failed) || 0;\n                if (successCount > 0) {\n                    toast({\n                        title: \"批量翻译完成\",\n                        description: \"成功翻译 \".concat(successCount, \" 个产品\").concat(failedCount > 0 ? \"，失败 \".concat(failedCount, \" 个\") : \"\"),\n                        variant: \"default\"\n                    });\n                } else {\n                    toast({\n                        title: \"批量翻译失败\",\n                        description: \"所有产品翻译失败，请检查产品数据后重试\",\n                        variant: \"destructive\"\n                    });\n                }\n                // 强制刷新产品列表，确保显示最新的翻译状态\n                console.log(\"批量翻译完成，开始刷新数据...\");\n                // 清除缓存并重新获取数据\n                await fetchUploadProducts({\n                    _t: Date.now(),\n                    force_refresh: true\n                });\n                console.log(\"第一次刷新完成，当前组件状态中的产品数据:\", uploadProducts.slice(0, 2).map((p)=>({\n                        id: p.id,\n                        sku: p.sku,\n                        multi_titles: p.multi_titles,\n                        multi_descriptions: p.multi_descriptions\n                    })));\n                console.log(\"刷新完成，refreshKey:\", refreshKey);\n                // 强制重新渲染组件\n                setRefreshKey((prev)=>prev + 1);\n                // 如果还是没有刷新，再次尝试\n                setTimeout(async ()=>{\n                    console.log(\"延迟刷新开始...\");\n                    await fetchUploadProducts({\n                        _t: Date.now()\n                    });\n                    setRefreshKey((prev)=>prev + 1);\n                    console.log(\"延迟刷新完成\");\n                }, 1000);\n            } else {\n                var _result_data1;\n                throw new Error(((_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message) || result.message || \"批量翻译失败\");\n            }\n        } catch (error) {\n            console.error(\"批量翻译失败:\", error);\n            toast({\n                description: error instanceof Error ? error.message : \"批量翻译时发生错误，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                className: \"bg-gray-100 text-gray-800\"\n            },\n            pending: {\n                label: \"待上架\",\n                className: \"bg-yellow-100 text-yellow-800\"\n            },\n            active: {\n                label: \"已上架\",\n                className: \"bg-green-100 text-green-800\"\n            },\n            failed: {\n                label: \"上架失败\",\n                className: \"bg-red-100 text-red-800\"\n            },\n            inactive: {\n                label: \"已下架\",\n                className: \"bg-gray-100 text-gray-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.draft;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 444,\n            columnNumber: 7\n        }, this);\n    };\n    const getTranslationStatusBadge = (status)=>{\n        const statusMap = {\n            pending: {\n                label: \"待翻译\",\n                className: \"bg-orange-100 text-orange-800\"\n            },\n            completed: {\n                label: \"已完成\",\n                className: \"bg-green-100 text-green-800\"\n            }\n        };\n        const config = statusMap[status] || statusMap.pending;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.className),\n            children: config.label\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 457,\n            columnNumber: 7\n        }, this);\n    };\n    // 获取平台显示名称\n    const getPlatformName = (platformCode)=>{\n        const platformMap = {\n            worten: \"Worten\",\n            phh: \"PHH\",\n            amazon: \"Amazon\",\n            ebay: \"eBay\",\n            shopify: \"Shopify\"\n        };\n        return platformMap[platformCode] || platformCode.toUpperCase();\n    };\n    // 获取平台所需的语言列表\n    const getPlatformRequiredLanguages = (platformCode)=>{\n        const platformLanguages = {\n            worten: [\n                \"PT\",\n                \"ES\"\n            ],\n            phh: [\n                \"LT\",\n                \"LV\",\n                \"EE\",\n                \"FI\"\n            ],\n            amazon: [\n                \"EN\",\n                \"DE\",\n                \"FR\",\n                \"IT\",\n                \"ES\"\n            ],\n            ebay: [\n                \"EN\"\n            ],\n            shopify: [\n                \"EN\"\n            ]\n        };\n        return platformLanguages[platformCode] || [\n            \"EN\"\n        ];\n    };\n    // 获取平台翻译目标语言（转换为LanguageCode格式）\n    const getPlatformTargetLanguages = (platformCode)=>{\n        const languageMap = {\n            \"PT\": \"pt\",\n            \"ES\": \"es\",\n            \"LT\": \"lt\",\n            \"LV\": \"lv\",\n            \"EE\": \"et\",\n            \"FI\": \"fi\",\n            \"EN\": \"en\",\n            \"DE\": \"zh\",\n            \"FR\": \"zh\",\n            \"IT\": \"zh\" // 暂时映射到中文，实际项目中需要支持意大利语\n        };\n        const platformLanguages = getPlatformRequiredLanguages(platformCode);\n        return platformLanguages.map((lang)=>languageMap[lang] || \"en\");\n    };\n    // 单个产品翻译处理\n    const handleProductTranslation = (product, contentType)=>{\n        setTranslationProduct(product);\n        setTranslationContentType(contentType);\n        setTranslationModalOpen(true);\n    };\n    // 批量翻译功能已移除，使用单个产品翻译\n    // 翻译完成处理\n    const handleTranslationComplete = async (translations)=>{\n        if (!translationProduct) return;\n        try {\n            // 构建更新数据\n            const updateData = {};\n            if (translationContentType === \"title\") {\n                updateData.multi_titles = {\n                    ...translationProduct.multi_titles || {},\n                    ...translations\n                };\n            } else {\n                updateData.multi_descriptions = {\n                    ...translationProduct.multi_descriptions || {},\n                    ...translations\n                };\n            }\n            // 更新翻译状态\n            updateData.listings_translation_status = \"completed\";\n            // 调用更新API\n            await updateUploadProduct(translationProduct.id, updateData);\n            toast({\n                title: \"翻译完成\",\n                description: \"\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \"翻译已保存\")\n            });\n            // 刷新数据\n            await fetchUploadProducts();\n        } catch (error) {\n            console.error(\"Translation save error:\", error);\n            toast({\n                title: \"保存失败\",\n                description: \"翻译结果保存失败，请重试\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // 渲染多语言状态组件 - 竖着排列，紧凑样式\n    const renderMultiLanguageStatus = (multiLangData, requiredLanguages)=>{\n        const data = multiLangData || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center space-y-1\",\n            children: requiredLanguages.map((lang)=>{\n                const hasTranslation = data[lang] && data[lang].trim() !== \"\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-1 py-0.5 rounded text-xs font-medium text-center w-8 \".concat(hasTranslation ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                    children: lang\n                }, lang, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 564,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedProducts(uploadProducts.map((p)=>p.id));\n        } else {\n            setSelectedProducts([]);\n        }\n    };\n    const handleSelectProduct = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts([\n                ...selectedProducts,\n                productId\n            ]);\n        } else {\n            setSelectedProducts(selectedProducts.filter((id)=>id !== productId));\n        }\n    };\n    // 处理产品行单击选中\n    const handleProductClick = (productId, event)=>{\n        // 防止复选框点击触发行点击\n        if (event.target.closest('input[type=\"checkbox\"]') || event.target.closest(\"button\")) {\n            return;\n        }\n        const isSelected = selectedProducts.includes(productId);\n        handleSelectProduct(productId, !isSelected);\n    };\n    // 显示加载状态\n    if (productsLoading && uploadProducts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 616,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 615,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误状态\n    if (productsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"加载失败: \",\n                            productsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                        onClick: ()=>fetchUploadProducts(),\n                        variant: \"outline\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 628,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n            lineNumber: 627,\n            columnNumber: 7\n        }, this);\n    }\n    const platformName = getPlatformName(platform);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[calc(100vh-4rem)]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"flex-1 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"p-0 flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowForm(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"新增上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"批量上架\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"同步状态\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            \"批量操作\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                    align: \"start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchTranslation,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量翻译\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            onClick: handleBatchDelete,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"批量导出\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"全部\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"店铺\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStore !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 706,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                        onClick: ()=>setSelectedStore(\"all\"),\n                                                                        children: \"全部店铺\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    platformStores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>setSelectedStore(store.id.toString()),\n                                                                            children: store.store_name\n                                                                        }, store.id, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"状态\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 729,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedStatuses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: selectedStatuses.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 735,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: statusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuCheckboxItem, {\n                                                                        checked: selectedStatuses.includes(option.value),\n                                                                        onCheckedChange: ()=>handleStatusToggle(option.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                lineNumber: 745,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, option.value, true, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 740,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"翻译状态\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 757,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedTranslationStatuses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: selectedTranslationStatuses.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                children: translationStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuCheckboxItem, {\n                                                                        checked: selectedTranslationStatuses.includes(option.value),\n                                                                        onCheckedChange: ()=>handleTranslationStatusToggle(option.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(option.icon, {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                lineNumber: 773,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, option.value, true, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"w-32 justify-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 784,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: \"创建时间\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        (dateRange.start || dateRange.end) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 787,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-auto flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 791,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                className: \"w-64 p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"开始日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 797,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.start,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                start: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 798,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 796,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"结束日期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 806,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                                                    type: \"date\",\n                                                                                    value: dateRange.end,\n                                                                                    onChange: (e)=>setDateRange((prev)=>({\n                                                                                                ...prev,\n                                                                                                end: e.target.value\n                                                                                            })),\n                                                                                    className: \"mt-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 807,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 805,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                        placeholder: \"搜索SKU、EAN、标题...\",\n                                                        value: searchValue,\n                                                        onChange: (e)=>setSearchValue(e.target.value),\n                                                        className: \"w-64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"搜索\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 827,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"重置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 820,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 11\n                        }, this),\n                        uploadProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col items-center justify-center text-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"暂无产品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm border-separate border-spacing-0 table-fixed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[35%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-[10%]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-28\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 857,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted/30 border-b h-14\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left border-r border-border/50 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.length === uploadProducts.length && uploadProducts.length > 0,\n                                                            onCheckedChange: handleSelectAll\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"标题/OfferID/店铺/分类\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-left h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"SKU/EAN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"库存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"售价（€）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言标题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"多语言描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"创建时间/发布时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-left font-medium text-muted-foreground h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: uploadProducts.map((product, index)=>{\n                                            var _stores_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 \".concat(selectedProducts.includes(product.id) ? \"bg-blue-50 border-blue-200\" : index % 2 === 0 ? \"bg-background\" : \"bg-muted/10\"),\n                                                onClick: (e)=>handleProductClick(product.id, e),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onCheckedChange: (checked)=>handleSelectProduct(product.id, checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 914,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden border shadow-sm\",\n                                                            children: product.image1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image1,\n                                                                alt: product.english_title || \"\",\n                                                                className: \"w-full h-full object-cover cursor-pointer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-muted-foreground text-xs\",\n                                                                children: \"无图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                lineNumber: 928,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 920,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    title: product.english_title || \"未设置标题\",\n                                                                    children: product.english_title || \"未设置标题\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground line-clamp-1\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        product.id,\n                                                                        \" | 店铺: \",\n                                                                        ((_stores_find = stores.find((s)=>s.id === product.store_id)) === null || _stores_find === void 0 ? void 0 : _stores_find.store_name) || \"未知店铺\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-muted-foreground text-xs line-clamp-1\",\n                                                                    children: [\n                                                                        \"分类: \",\n                                                                        product.platform_category_id || \"未设置\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: getStatusBadge(product.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_sku || product.sku\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 950,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-foreground leading-tight line-clamp-1\",\n                                                                    children: product.upstores_ean || product.ean\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 951,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-blue-600 text-sm\",\n                                                            children: product.stock_quantity || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-green-600 text-sm\",\n                                                            children: product.discounted_price ? \"€\".concat(product.discounted_price) : \"未设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_titles || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 962,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50 text-center\",\n                                                        children: renderMultiLanguageStatus(product.multi_descriptions || null, getPlatformRequiredLanguages(platform))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r border-border/50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: new Date(product.uplisting_at).toLocaleString(\"zh-CN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 974,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 983,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 981,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleEditProduct(product),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 988,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"编辑产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 987,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 992,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"复制到其他店铺\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 991,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 995,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 998,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"提交上架\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 997,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1004,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"同步状态\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1003,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        product.listings_translation_status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"title\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1011,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译标题\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1010,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleProductTranslation(product, \"description\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                            lineNumber: 1015,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"翻译描述\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1014,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1021,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"查看原产品\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1020,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1024,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                                            className: \"text-red-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChevronDown_Clock_Copy_Download_Edit_ExternalLink_FileText_Filter_Languages_MoreHorizontal_Plus_RefreshCw_RotateCcw_Search_Store_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                                    lineNumber: 1029,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"删除刊登\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                            lineNumber: 1025,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, \"\".concat(product.id, \"-\").concat(refreshKey), true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 845,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"显示 \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \"-\",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" 条，共 \",\n                                        pagination.total,\n                                        \" 条记录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page <= 1,\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page - 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"上一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1048,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, Math.ceil(pagination.total / pagination.limit))\n                                        }, (_, i)=>{\n                                            const pageNum = Math.max(1, pagination.page - 2) + i;\n                                            if (pageNum > Math.ceil(pagination.total / pagination.limit)) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                size: \"sm\",\n                                                variant: pageNum === pagination.page ? \"default\" : \"outline\",\n                                                onClick: ()=>fetchUploadProducts({\n                                                        page: pageNum\n                                                    }),\n                                                className: \"h-8 w-8 p-0 text-xs\",\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                                lineNumber: 1064,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            disabled: pagination.page >= Math.ceil(pagination.total / pagination.limit),\n                                            onClick: ()=>fetchUploadProducts({\n                                                    page: pagination.page + 1\n                                                }),\n                                            className: \"h-8 px-3 text-xs\",\n                                            children: \"下一页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                            lineNumber: 1076,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                                    lineNumber: 1047,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                            lineNumber: 1043,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                    lineNumber: 643,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 642,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_uploadproduct_upload_product_form__WEBPACK_IMPORTED_MODULE_7__.UploadProductForm, {\n                open: showForm,\n                onClose: handleCloseForm,\n                onSubmit: handleFormSubmit,\n                platform: platform,\n                stores: stores,\n                editingProduct: editingProduct,\n                mode: editingProduct ? \"edit\" : \"add\"\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1091,\n                columnNumber: 7\n            }, this),\n            translationProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation__WEBPACK_IMPORTED_MODULE_8__.TranslationModal, {\n                open: translationModalOpen,\n                onOpenChange: setTranslationModalOpen,\n                title: \"翻译\".concat(translationContentType === \"title\" ? \"标题\" : \"描述\", \" - \").concat(translationProduct.sku),\n                initialText: translationContentType === \"title\" ? translationProduct.english_title || \"\" : ((_translationProduct_multi_descriptions = translationProduct.multi_descriptions) === null || _translationProduct_multi_descriptions === void 0 ? void 0 : _translationProduct_multi_descriptions.EN) || \"\",\n                sourceLang: \"en\",\n                targetLangs: getPlatformTargetLanguages(platform),\n                contentType: translationContentType,\n                platform: platform,\n                source: \"form_batch\" // 批量翻译场景\n                ,\n                onTranslationComplete: handleTranslationComplete,\n                onTranslationError: (errors)=>{\n                    console.error(\"Translation errors:\", errors);\n                    toast({\n                        title: \"翻译失败\",\n                        description: \"部分语言翻译失败，请查看详情\",\n                        variant: \"destructive\"\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n                lineNumber: 1103,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\uploadproduct\\\\worten\\\\worten-listing-page.tsx\",\n        lineNumber: 641,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadProductPageFull, \"AyWzKMoVGB62XyLBNEOJtbP8lfQ=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_5__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _components_ui_batch_translation_progress__WEBPACK_IMPORTED_MODULE_9__.useBatchTranslationProgress,\n        _hooks_useUploadProducts__WEBPACK_IMPORTED_MODULE_2__.useUploadProducts,\n        _hooks_useStores__WEBPACK_IMPORTED_MODULE_3__.useStores,\n        _hooks_useDropshipProducts__WEBPACK_IMPORTED_MODULE_4__.useDropshipProducts\n    ];\n});\n_c = UploadProductPageFull;\nvar _c;\n$RefreshReg$(_c, \"UploadProductPageFull\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/uploadproduct/worten/worten-listing-page.tsx\n"));

/***/ })

});